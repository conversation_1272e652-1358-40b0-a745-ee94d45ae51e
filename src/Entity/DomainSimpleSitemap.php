<?php

namespace Drupal\domain_simple_sitemap\Entity;

use Drupal\simple_sitemap\Entity\SimpleSitemap;

class DomainSimpleSitemap extends SimpleSitemap {

  /**
   * {@inheritdoc}
   */
  public function toUrl($rel = 'canonical', array $options = []) {
    if ($rel !== 'canonical') {
      return parent::toUrl($rel, $options);
    }

    if (empty($options['base_url'])) {
      // Try to get domain's path.
      $sitemap_domain = $this->getThirdPartySetting('domain_simple_sitemap', 'sitemap_domain');
      if ($sitemap_domain) {
        $domain = $this->entityTypeManager()->getStorage('domain')->load($sitemap_domain);
        if ($domain) {
          $options['base_url'] = rtrim($domain->getPath(), '/');
        }
      }
    }

    return parent::toUrl($rel, $options);
  }

}
