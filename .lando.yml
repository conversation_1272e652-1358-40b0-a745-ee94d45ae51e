# Fill in the name, proxy and evironment settings to those specific to your project..

# This file should be commited to the repository

# recipe can be drupal9, drupal7, wordpress
recipe: drupal10

name: mhr

config:
  xdebug: true
  php: 8.3
  webroot: docroot

proxy:
  appserver:
    - hostname: mhr.local.versantus.co.uk
  mailhog:
    - mail.mhr.local.versantus.co.uk

services:
  appserver:
    overrides:
      environment:
        ACQUIA_APPLICATION: "mhrglobal"
        DRUSH_OPTIONS_URI: "https://mhr.local.versantus.co.uk"

	# The following vars are used by lando vits_copy_live to extract the database from the backup server.
        LIVE_SITE_URL:  "https://www.mhrglobal.com"
        LIVE_SERVER_HOST: "ernie.versantus.co.uk"
        LIVE_SERVER_USER: "mhrglobal"
        PRIMARY_SITE_DOMAIN: "mhrglobal.com"

  node:
    overrides:
      environment:
        # This is delibrately set to /app/drupal because that is where package.lock is stored for this site.
        THEME_DIR: "/app/drupal"


tooling:
  compile_css:
    description: "Compiles SASS files into CSS"
    service: node
    cmd: "npm prefix /app/drupal install && npm --prefix /app/drupal run build"
  compile_js:
    description: "Minifies JS"
    service: node
    cmd: "npm prefix /app/drupal install && npm --prefix /app/drupal run minifyJS"
  create_htaccess_patch:
    description: "Create the .htaccess patch to apply post drupal install"
    service: appserver
    cmd: "SHELL=/bin/bash cd /app ; diff -u config/.htaccess_default config/.htaccess_edited | sed -e 's/htaccess_default/htaccess/' -e 's/htaccess_edited/htaccess/'  > patches/htaccess.patch"
