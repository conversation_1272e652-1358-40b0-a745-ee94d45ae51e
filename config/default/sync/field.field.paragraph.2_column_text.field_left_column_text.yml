uuid: aa992a77-421f-4fda-bab9-24854d249d76
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_left_column_text
    - paragraphs.paragraphs_type.2_column_text
  module:
    - text
id: paragraph.2_column_text.field_left_column_text
field_name: field_left_column_text
entity_type: paragraph
bundle: 2_column_text
label: 'Left column text'
description: 'Text to be displayed in left column'
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - mhr_limited_html
field_type: text_long
