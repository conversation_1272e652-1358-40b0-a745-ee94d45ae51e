uuid: 33179a77-9fe7-43b2-9bfd-98c97200e547
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_body
    - paragraphs.paragraphs_type.image_and_text
  module:
    - text
id: paragraph.image_and_text.field_body
field_name: field_body
entity_type: paragraph
bundle: image_and_text
label: Body
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - mhr_limited_html
field_type: text_long
