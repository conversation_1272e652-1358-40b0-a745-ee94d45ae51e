uuid: 2eddfa28-ed86-44d2-b5ce-fa51b772d305
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.documentation.field_domain_access
    - field.field.node.documentation.field_domain_all_affiliates
    - field.field.node.documentation.field_domain_source
    - field.field.node.documentation.field_page_title_display
    - field.field.node.documentation.field_rows
    - field.field.node.documentation.field_top_page_content
    - node.type.documentation
  module:
    - user
id: node.documentation.teaser
targetEntityType: node
bundle: documentation
mode: teaser
content:
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  field_page_title_display: true
  field_rows: true
  field_top_page_content: true
  langcode: true
  search_api_excerpt: true
