uuid: a8c2d753-c7f2-4f42-8523-2e43da18d7a9
langcode: en
status: true
dependencies:
  config:
    - field.field.media.document.field_media_document
    - media.type.document
  module:
    - file
    - path
_core:
  default_config_hash: gtZlAQdQqZgVWvvZ6v-hfmACkfEimqn_GPDbQZqNAbw
id: media.document.default
targetEntityType: media
bundle: document
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_document:
    type: file_generic
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  replace_file:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  name: true
