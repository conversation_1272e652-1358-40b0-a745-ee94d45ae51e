uuid: da2b56af-880f-4e45-80b2-8ba6d7d27072
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mhr_hwi_icon
    - paragraphs.paragraphs_type.mhr_heading_with_icon
    - taxonomy.vocabulary.mhr_icons
id: paragraph.mhr_heading_with_icon.field_mhr_hwi_icon
field_name: field_mhr_hwi_icon
entity_type: paragraph
bundle: mhr_heading_with_icon
label: Icon
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      mhr_icons: mhr_icons
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
