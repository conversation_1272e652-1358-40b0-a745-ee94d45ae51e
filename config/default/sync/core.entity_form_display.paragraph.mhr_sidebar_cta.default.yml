uuid: 46b8fb90-0696-4061-8b5e-d12813722138
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.mhr_sidebar_cta.field_mhr_sidebar_cta_bg_colour
    - field.field.paragraph.mhr_sidebar_cta.field_mhr_sidebar_cta_image
    - field.field.paragraph.mhr_sidebar_cta.field_mhr_sidebar_cta_link
    - field.field.paragraph.mhr_sidebar_cta.field_mhr_sidebar_cta_text
    - paragraphs.paragraphs_type.mhr_sidebar_cta
  module:
    - link
    - media_library
    - text
id: paragraph.mhr_sidebar_cta.default
targetEntityType: paragraph
bundle: mhr_sidebar_cta
mode: default
content:
  field_mhr_sidebar_cta_bg_colour:
    type: options_select
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_mhr_sidebar_cta_image:
    type: media_library_widget
    weight: 1
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_mhr_sidebar_cta_link:
    type: link_default
    weight: 3
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_mhr_sidebar_cta_text:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 2
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
