uuid: c42d8b8b-da88-44b4-b7e6-54cd2d4761e0
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_meta_tags
    - node.type.article
  module:
    - metatag
id: node.article.field_meta_tags
field_name: field_meta_tags
entity_type: node
bundle: article
label: 'Meta tags'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag
