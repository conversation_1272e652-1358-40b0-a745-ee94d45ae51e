uuid: ac393c31-2292-4b10-938b-8d36c880eeee
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_domain_access
    - node.type.pillar_page
id: node.pillar_page.field_domain_access
field_name: field_domain_access
entity_type: node
bundle: pillar_page
label: 'Domain Access'
description: 'Select the affiliate domain(s) for this content'
required: true
translatable: true
default_value: {  }
default_value_callback: 'Drupal\domain_access\DomainAccessManager::getDefaultValue'
settings:
  handler: 'default:domain'
  handler_settings:
    target_bundles: null
    sort:
      field: weight
      direction: ASC
field_type: entity_reference
