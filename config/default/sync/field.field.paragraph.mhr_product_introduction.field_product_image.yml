uuid: 9b5bdce6-2f15-47d2-ba70-addba1d8ef0f
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_product_image
    - media.type.image
    - paragraphs.paragraphs_type.mhr_product_introduction
id: paragraph.mhr_product_introduction.field_product_image
field_name: field_product_image
entity_type: paragraph
bundle: mhr_product_introduction
label: 'Product image'
description: 'Image should be at least 1200x500 px. Larger images will be scaled/cropped to this size'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
