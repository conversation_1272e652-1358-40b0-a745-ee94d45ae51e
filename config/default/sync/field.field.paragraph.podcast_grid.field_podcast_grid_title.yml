uuid: 44676853-e7f7-4722-8746-f3998471aa4b
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_podcast_grid_title
    - paragraphs.paragraphs_type.podcast_grid
id: paragraph.podcast_grid.field_podcast_grid_title
field_name: field_podcast_grid_title
entity_type: paragraph
bundle: podcast_grid
label: 'Podcast Grid Title'
description: "Title for the podcast grid . eg:'Latest Podcasts'"
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
