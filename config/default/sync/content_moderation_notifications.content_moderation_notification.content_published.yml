uuid: 1ff08617-3462-48cf-83b4-fd5e6d823c03
langcode: en
status: true
dependencies: {  }
id: content_published
workflow: editorial
transitions:
  publish: publish
roles:
  content_moderator: content_moderator
author: false
site_mail: true
emails: ''
subject: 'Content has been published'
body:
  value: "<p>The following content has just been published:</p>\r\n\r\n<p>[node:title] -&nbsp;[node:url]</p>\r\n\r\n<p>This could be a result of changes to an already published page or a new page being published for the first time.</p>\r\n\r\n<p>Please review this content.</p>\r\n"
  format: mhr_html
label: 'Content Published'
