uuid: 952296ea-35bd-47d7-8305-71a484ab5f0f
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.podcast_listing_item
  module:
    - text
id: node.podcast_listing_item.body
field_name: body
entity_type: node
bundle: podcast_listing_item
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
  allowed_formats: {  }
field_type: text_with_summary
