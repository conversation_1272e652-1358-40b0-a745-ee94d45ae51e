uuid: 970d4b00-ae44-48c4-aa7b-85b444e47246
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content_hub_resource
    - node.type.video_listing_item
    - taxonomy.vocabulary.resource_type
  content:
    - 'taxonomy_term:resource_type:47eddf05-d682-4b5f-9cf6-df597690535e'
id: node.video_listing_item.field_content_hub_resource
field_name: field_content_hub_resource
entity_type: node
bundle: video_listing_item
label: Resource
description: ''
required: false
translatable: true
default_value:
  -
    target_uuid: 47eddf05-d682-4b5f-9cf6-df597690535e
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      resource_type: resource_type
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
