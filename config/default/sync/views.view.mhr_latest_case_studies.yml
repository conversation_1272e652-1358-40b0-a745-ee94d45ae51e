uuid: 1ca00b5b-de80-4f3f-a90a-5e124fe8ccfe
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - node.type.case_study
    - taxonomy.vocabulary.industry_tags
  module:
    - datetime
    - domain_access
    - node
    - taxonomy
    - user
    - views_autosubmit
id: mhr_latest_case_studies
label: 'MHR Latest case studies'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: ''
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      exposed_form:
        type: autosubmit
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          autosubmit_hide: true
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: none
        options: {  }
      empty: {  }
      sorts:
        field_published_date_value:
          id: field_published_date_value
          table: node__field_published_date
          field: field_published_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments:
        field_related_industry_target_id:
          id: field_related_industry_target_id
          table: node__field_related_industry
          field: field_related_industry_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: true
          not: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            case_study: case_study
          expose:
            operator_limit_selection: false
            operator_list: {  }
        current_all:
          id: current_all
          table: node__field_domain_access
          field: current_all
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: domain_access_current_all_filter
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_related_industry_target_id:
          id: field_related_industry_target_id
          table: node__field_related_industry
          field: field_related_industry_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_related_industry_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_related_industry_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_related_industry_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              client_administrator: '0'
              content_moderator: '0'
              client_editor: '0'
              blog_moderator: '0'
              blog_editor: '0'
              content_publisher: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: industry_tags
          type: select
          hierarchy: false
          limit: true
          error_message: true
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: teaser
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.site
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  display_3_items:
    id: display_3_items
    display_title: 'Display 3 items'
    display_plugin: block
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      defaults:
        pager: false
      display_description: ''
      exposed_block: false
      display_extenders: {  }
      block_description: display_3_items
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.site
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  display_6_items:
    id: display_6_items
    display_title: 'Display 6 items'
    display_plugin: block
    position: 2
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 6
      defaults:
        pager: false
      display_description: ''
      exposed_block: false
      display_extenders: {  }
      block_description: display_6_items
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.site
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  display_9_items:
    id: display_9_items
    display_title: 'Display 9 Items'
    display_plugin: block
    position: 3
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 9
      defaults:
        pager: false
      display_description: ''
      exposed_block: false
      display_extenders: {  }
      block_description: display_9_items
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.site
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  display_all_items:
    id: display_all_items
    display_title: 'Display all items'
    display_plugin: block
    position: 4
    display_options:
      pager:
        type: none
        options:
          offset: 0
      defaults:
        pager: false
      display_description: ''
      exposed_block: false
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.site
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
