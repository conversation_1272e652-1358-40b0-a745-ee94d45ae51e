uuid: 5340295d-**************-91d65f708166
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_mhr_partner_has_full_page
    - node.type.mhr_partner_integration
  module:
    - options
id: node.mhr_partner_integration.field_mhr_partner_has_full_page
field_name: field_mhr_partner_has_full_page
entity_type: node
bundle: mhr_partner_integration
label: 'Has associated full page?'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
