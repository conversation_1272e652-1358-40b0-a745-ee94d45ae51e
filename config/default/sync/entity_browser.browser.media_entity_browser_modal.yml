uuid: 12907961-6e88-40b0-b362-fbf40e4a5737
langcode: en
status: true
dependencies:
  config:
    - views.view.media_entity_browser
  module:
    - entity_browser_entity_form
    - views
_core:
  default_config_hash: VOi0Ua3BAuJlwzee4EWw-XHxybYY-IqVBetnROZff_I
name: media_entity_browser_modal
label: 'Media Entity Browser (Modal)'
display: modal
display_configuration:
  width: '1070'
  height: '500'
  link_text: 'Select media'
  auto_open: true
selection_display: no_display
selection_display_configuration: {  }
widget_selector: tabs
widget_selector_configuration: {  }
widgets:
  fc729026-c85f-4561-acd5-38d4082b69fb:
    id: view
    uuid: fc729026-c85f-4561-acd5-38d4082b69fb
    label: 'Choose existing media'
    weight: -9
    settings:
      submit_text: 'Select media'
      auto_select: false
      view: media_entity_browser
      view_display: media_browser_all
  9769d4e4-1f9c-4313-8b01-fe8cff8aa5a1:
    id: entity_form
    uuid: 9769d4e4-1f9c-4313-8b01-fe8cff8aa5a1
    label: 'Upload new image'
    weight: -10
    settings:
      submit_text: 'Save image'
      entity_type: media
      bundle: image
      form_mode: default
