uuid: 3e89b3d8-7abd-4c15-becb-8dc0432764e7
langcode: en
status: true
dependencies:
  config:
    - field.field.user.user.field_domain_access
    - field.field.user.user.field_domain_admin
    - field.field.user.user.field_domain_all_affiliates
    - field.field.user.user.user_picture
  module:
    - path
    - user
_core:
  default_config_hash: K-1rBM8mTIkFp9RqOC2tMRUukOQ1xbRCfSKK8dEddnA
id: user.user.default
targetEntityType: user
bundle: user
mode: default
content:
  account:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_domain_access:
    type: options_buttons
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_domain_admin:
    type: options_buttons
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_domain_all_affiliates:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  language:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  contact: true
  langcode: true
  timezone: true
  user_picture: true
