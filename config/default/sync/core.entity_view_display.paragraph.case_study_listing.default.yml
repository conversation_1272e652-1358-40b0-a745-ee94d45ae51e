uuid: f782d9e4-7c73-4c77-9a3c-76e390952db6
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.case_study_listing.field_display_options
    - field.field.paragraph.case_study_listing.field_mhr_component_options
    - field.field.paragraph.case_study_listing.field_related_industry
    - field.field.paragraph.case_study_listing.field_row_background_colour
    - field.field.paragraph.case_study_listing.field_tags
    - field.field.paragraph.case_study_listing.field_title
    - paragraphs.paragraphs_type.case_study_listing
  module:
    - options
    - string_field_formatter
id: paragraph.case_study_listing.default
targetEntityType: paragraph
bundle: case_study_listing
mode: default
content:
  field_display_options:
    type: list_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_related_industry:
    type: entity_reference_label
    label: hidden
    settings:
      link: true
    third_party_settings: {  }
    weight: 3
    region: content
  field_tags:
    type: entity_reference_label
    label: hidden
    settings:
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_title:
    type: plain_string_formatter
    label: hidden
    settings:
      link_to_entity: false
      wrap_tag: h2
      wrap_class: ''
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_mhr_component_options: true
  field_row_background_colour: true
  search_api_excerpt: true
