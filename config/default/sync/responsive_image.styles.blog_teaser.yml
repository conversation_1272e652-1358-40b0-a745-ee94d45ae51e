uuid: a7bc7b55-de4a-48fc-a86d-70a7ceffe012
langcode: en
status: true
dependencies:
  config:
    - image.style.blog_teaser_extra_wide
    - image.style.blog_teaser_medium
    - image.style.blog_teaser_mobile
    - image.style.blog_teaser_narrow
    - image.style.blog_teaser_wide
  theme:
    - mhr
id: blog_teaser
label: 'Blog teaser'
image_style_mappings:
  -
    image_mapping_type: image_style
    image_mapping: blog_teaser_extra_wide
    breakpoint_id: mhr.desktop-large
    multiplier: 1x
  -
    image_mapping_type: image_style
    image_mapping: blog_teaser_wide
    breakpoint_id: mhr.desktop
    multiplier: 1x
  -
    image_mapping_type: image_style
    image_mapping: blog_teaser_medium
    breakpoint_id: mhr.tablet
    multiplier: 1x
  -
    image_mapping_type: image_style
    image_mapping: blog_teaser_narrow
    breakpoint_id: mhr.narrow
    multiplier: 1x
  -
    image_mapping_type: image_style
    image_mapping: blog_teaser_mobile
    breakpoint_id: mhr.mobile
    multiplier: 1x
breakpoint_group: mhr
fallback_image_style: blog_teaser_extra_wide
