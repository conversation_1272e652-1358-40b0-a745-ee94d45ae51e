uuid: ea1f62e8-0619-4b42-9e05-e8e7c0b414d6
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.mhr_handwritten_text_cta.field_cta_button_universal
    - field.field.paragraph.mhr_handwritten_text_cta.field_mhr_htcta_heading
    - field.field.paragraph.mhr_handwritten_text_cta.field_mhr_htcta_text
    - field.field.paragraph.mhr_handwritten_text_cta.field_mhr_htcta_word_to_show
    - paragraphs.paragraphs_type.mhr_handwritten_text_cta
  module:
    - paragraphs
id: paragraph.mhr_handwritten_text_cta.default
targetEntityType: paragraph
bundle: mhr_handwritten_text_cta
mode: default
content:
  field_cta_button_universal:
    type: paragraphs
    weight: 3
    region: content
    settings:
      title: 'CTA Button'
      title_plural: 'CTA Buttons'
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: cta_button
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_mhr_htcta_heading:
    type: paragraphs
    weight: 1
    region: content
    settings:
      title: Heading
      title_plural: Headings
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: mhr_heading_with_icon
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_mhr_htcta_text:
    type: string_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_mhr_htcta_word_to_show:
    type: options_select
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
