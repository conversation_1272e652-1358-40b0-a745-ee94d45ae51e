uuid: e80b38f2-992b-4a74-8360-c2c168684c20
langcode: en
status: true
dependencies:
  config:
    - field.field.node.documentation.field_domain_access
    - field.field.node.documentation.field_domain_all_affiliates
    - field.field.node.documentation.field_domain_source
    - field.field.node.documentation.field_page_title_display
    - field.field.node.documentation.field_rows
    - field.field.node.documentation.field_top_page_content
    - node.type.documentation
  module:
    - entity_reference_revisions
    - layout_paragraphs
    - user
id: node.documentation.default
targetEntityType: node
bundle: documentation
mode: default
content:
  field_rows:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 1
    region: content
  field_top_page_content:
    type: layout_paragraphs
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 0
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  field_page_title_display: true
  langcode: true
  search_api_excerpt: true
