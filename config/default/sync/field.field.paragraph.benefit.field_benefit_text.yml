uuid: 5f131a51-2269-4239-8b57-e029d50cb9cd
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_benefit_text
    - paragraphs.paragraphs_type.benefit
  module:
    - text
id: paragraph.benefit.field_benefit_text
field_name: field_benefit_text
entity_type: paragraph
bundle: benefit
label: 'Benefit text'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - mhr_limited_html
field_type: text
