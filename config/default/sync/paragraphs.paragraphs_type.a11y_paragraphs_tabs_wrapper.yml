uuid: a811cd3c-6ff2-42c1-bb8d-52e02b1d0748
langcode: en
status: true
dependencies:
  content:
    - 'file:file:3e6a5e31-417a-471c-933b-35b43172008e'
_core:
  default_config_hash: fzN86znID-9S-OOiu85pkdTeyi4jKfa1yVPi9x-zd6o
id: a11y_paragraphs_tabs_wrapper
label: 'Paragraphs Tabs Wrapper'
icon_uuid: 3e6a5e31-417a-471c-933b-35b43172008e
icon_default: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgICA8Zz4KICAgICAgICA8cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+CiAgICAgICAgPHBhdGggZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNMyAyMWExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWgxOGExIDEgMCAwIDEgMSAxdjE2YTEgMSAwIDAgMS0xIDFIM3ptNC0xMUg0djloM3YtOXptMTMgMEg5djloMTF2LTl6bTAtNUg0djNoMTZWNXoiLz4KICAgIDwvZz4KPC9zdmc+Cg=='
description: 'Adds a wrapper for the A11Y Paragraphs Tabs Panel paragraph. This in turn creates tabs that comply to Accessibility (A11Y) standards and on mobile it becomes an accordion.'
behavior_plugins: {  }
