uuid: 14d9bb4c-63a5-4698-8bfe-7f5e619d15cc
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mhr_mcs_appearance
    - paragraphs.paragraphs_type.mixed_content_stripe
  module:
    - options
id: paragraph.mixed_content_stripe.field_mhr_mcs_appearance
field_name: field_mhr_mcs_appearance
entity_type: paragraph
bundle: mixed_content_stripe
label: Appearance
description: ''
required: true
translatable: false
default_value:
  -
    value: standardcarousel
default_value_callback: ''
settings: {  }
field_type: list_string
