uuid: 6e3885db-156d-4afd-b40b-120285c2b9e5
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.heading_text_image_link.field_anchor_link_id
    - field.field.paragraph.heading_text_image_link.field_body
    - field.field.paragraph.heading_text_image_link.field_heading
    - field.field.paragraph.heading_text_image_link.field_image
    - field.field.paragraph.heading_text_image_link.field_url
    - paragraphs.paragraphs_type.heading_text_image_link
  module:
    - link
    - text
id: paragraph.heading_text_image_link.default
targetEntityType: paragraph
bundle: heading_text_image_link
mode: default
content:
  field_anchor_link_id:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_heading:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: product_image
      link: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_url:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 4
    region: content
hidden: {  }
