uuid: 1b89fcc0-6855-4d37-b97d-e8b7f10126cb
langcode: de
status: true
dependencies: {  }
_core:
  default_config_hash: mMxmqv8O2gX-TwO3JIYiyNesiWfBxOHH8Rjc2tp4zxc
machine_name: paragraphs_base
name: 'Paragraphs Base'
description: ''
assignments:
  base:
    enabled: true
    weight: -2
    types:
      config:
        node_type: node_type
        paragraphs_type: paragraphs_type
      content: {  }
  core:
    enabled: false
    weight: 5
    types:
      config:
        date_format: date_format
        field_storage_config: field_storage_config
        entity_form_mode: entity_form_mode
        image_style: image_style
        menu: menu
        responsive_image_style: responsive_image_style
        user_role: user_role
        entity_view_mode: entity_view_mode
  dependency:
    enabled: false
    weight: 15
  exclude:
    enabled: true
    weight: -5
    types:
      config: {  }
    curated: true
    module:
      installed: true
      profile: true
      namespace: true
      namespace_any: false
  existing:
    enabled: false
    weight: 12
  forward_dependency:
    enabled: true
    weight: 4
  namespace:
    enabled: true
    weight: 0
  optional:
    enabled: false
    weight: 0
    types:
      config: {  }
  packages:
    enabled: false
    weight: -20
  profile:
    enabled: false
    weight: 10
    curated: true
    standard:
      files: true
      dependencies: true
    types:
      config:
        block: block
        language_content_settings: language_content_settings
        configurable_language: configurable_language
        migration: migration
        shortcut_set: shortcut_set
        tour: tour
  site:
    enabled: false
    weight: 7
    types:
      config:
        action: action
        contact_form: contact_form
        block_content_type: block_content_type
        rdf_mapping: rdf_mapping
        search_page: search_page
        taxonomy_vocabulary: taxonomy_vocabulary
        editor: editor
        filter_format: filter_format
profile_name: standard
is_profile: false
