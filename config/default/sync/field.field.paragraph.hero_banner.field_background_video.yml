uuid: 9038e173-ca9d-4b0e-8a76-9aa9253374c0
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_background_video
    - media.type.video
    - paragraphs.paragraphs_type.hero_banner
id: paragraph.hero_banner.field_background_video
field_name: field_background_video
entity_type: paragraph
bundle: hero_banner
label: 'Background Video'
description: 'Video should be at 1280px x 720px. If both background image and background video are added, background video will be used.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      video: video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: image
field_type: entity_reference
