uuid: b4bb01cd-b104-4b03-8c83-e861622adbb6
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_domain_access
    - node.type.podcast_listing_item
id: node.podcast_listing_item.field_domain_access
field_name: field_domain_access
entity_type: node
bundle: podcast_listing_item
label: 'Domain Access'
description: 'Select the affiliate domain(s) for this content'
required: true
translatable: true
default_value: {  }
default_value_callback: 'Drupal\domain_access\DomainAccessManager::getDefaultValue'
settings:
  handler: 'default:domain'
  handler_settings:
    target_bundles: null
    sort:
      field: weight
      direction: ASC
field_type: entity_reference
