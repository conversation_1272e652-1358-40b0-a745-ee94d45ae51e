uuid: 4dc86107-3226-4e03-9341-0ad428d84eaa
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_hero_banner_depth
    - node.type.webinar
  module:
    - options
id: node.webinar.field_hero_banner_depth
field_name: field_hero_banner_depth
entity_type: node
bundle: webinar
label: 'Hero banner depth'
description: 'If shallow, is chosen the height of the banner will be less tall than the standard height. The banner will be as tall as necessary to contain its text content.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
