uuid: 5d34b5a2-4e11-4cd7-a683-dfc1d4e2c248
langcode: en
status: true
dependencies:
  config:
    - media.type.video
  module:
    - image
id: media.video.thumbnail
field_name: thumbnail
entity_type: media
bundle: video
label: Thumbnail
description: 'The thumbnail of the media item.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: default
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: null
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
