uuid: 3602b6a6-d314-4e7c-8fbb-e66c8179f71d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content_hub_topic
    - node.type.blog_post
    - taxonomy.vocabulary.topic
id: node.blog_post.field_content_hub_topic
field_name: field_content_hub_topic
entity_type: node
bundle: blog_post
label: Topic
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      topic: topic
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
