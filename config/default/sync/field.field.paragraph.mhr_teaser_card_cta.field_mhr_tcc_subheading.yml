uuid: 7c68d26a-421a-40e7-8d77-34c810d9cdb8
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mhr_tcc_subheading
    - paragraphs.paragraphs_type.mhr_teaser_card_cta
id: paragraph.mhr_teaser_card_cta.field_mhr_tcc_subheading
field_name: field_mhr_tcc_subheading
entity_type: paragraph
bundle: mhr_teaser_card_cta
label: Subheading
description: 'The subheading is output in lines with a character limit of 36.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string_long
