uuid: c936baa7-e686-4843-89af-af99d47531f2
langcode: en
status: true
dependencies:
  module:
    - options
    - paragraphs
id: paragraph.field_mhr_tcc_appearance
field_name: field_mhr_tcc_appearance
entity_type: paragraph
type: list_string
settings:
  allowed_values:
    -
      value: flow
      label: "Flow (cards grouped in rows with 'flow' line joining their backgrounds on larger screens)"
    -
      value: carousel
      label: 'Carousel (carousel style with central card enlarged, and cards with a full-background image style)'
    -
      value: grid
      label: 'Grid (cards grouped in rows, and cards with a full-background image style)'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
