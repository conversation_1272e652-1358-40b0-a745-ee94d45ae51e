uuid: 5164b379-8108-446a-806c-fc4b1a267de9
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.content_hub_teaser
    - field.field.node.podcast_listing_item.body
    - field.field.node.podcast_listing_item.field_button
    - field.field.node.podcast_listing_item.field_content_hub_featured
    - field.field.node.podcast_listing_item.field_content_hub_product
    - field.field.node.podcast_listing_item.field_content_hub_resource
    - field.field.node.podcast_listing_item.field_content_hub_topic
    - field.field.node.podcast_listing_item.field_domain_access
    - field.field.node.podcast_listing_item.field_domain_all_affiliates
    - field.field.node.podcast_listing_item.field_domain_source
    - field.field.node.podcast_listing_item.field_heading
    - field.field.node.podcast_listing_item.field_published_date
    - field.field.node.podcast_listing_item.field_resource_image
    - field.field.node.podcast_listing_item.field_tags
    - node.type.podcast_listing_item
  module:
    - link
    - smart_trim
    - user
id: node.podcast_listing_item.content_hub_teaser
targetEntityType: node
bundle: podcast_listing_item
mode: content_hub_teaser
content:
  body:
    type: smart_trim
    label: hidden
    settings:
      trim_length: 30
      trim_type: words
      trim_suffix: ''
      wrap_output: false
      wrap_class: trimmed
      more:
        display_link: false
        target_blank: false
        link_trim_only: false
        class: more-link
        text: More
        aria_label: 'Read more about [node:title]'
      summary_handler: full
      trim_options:
        text: false
        trim_zero: false
    third_party_settings: {  }
    weight: 3
    region: content
  content_moderation_control:
    settings: {  }
    third_party_settings: {  }
    weight: -20
    region: content
  field_button:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 4
    region: content
  field_heading:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_resource_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: content_hub_card_teaser
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_content_hub_featured: true
  field_content_hub_product: true
  field_content_hub_resource: true
  field_content_hub_topic: true
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  field_published_date: true
  field_tags: true
  langcode: true
  search_api_excerpt: true
