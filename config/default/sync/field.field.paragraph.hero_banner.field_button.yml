uuid: 5cd1af1f-64ed-4a3b-9591-f5443cc8e9a5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_button
    - paragraphs.paragraphs_type.hero_banner
  module:
    - link
id: paragraph.hero_banner.field_button
field_name: field_button
entity_type: paragraph
bundle: hero_banner
label: 'But<PERSON> (Legacy)'
description: 'Call to action button'
required: false
translatable: true
default_value:
  -
    attributes: {  }
    uri: 'internal:#'
    title: 'Find out more'
    options: {  }
default_value_callback: ''
settings:
  title: 2
  link_type: 17
field_type: link
