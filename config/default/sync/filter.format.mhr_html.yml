uuid: 7a7a5fcd-ac8c-463a-a593-883881d09ecf
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.mhr_embedded_image
  module:
    - blazy
    - editor
    - linkit
    - media
    - pathologic
    - slick
    - url_embed
name: 'MHR HTML'
format: mhr_html
weight: -10
filters:
  blazy_filter:
    id: blazy_filter
    provider: blazy
    status: false
    weight: -42
    settings:
      media_switch: ''
      hybrid_style: ''
      box_style: ''
      box_caption: ''
      filter_tags:
        img: img
        iframe: iframe
      use_data_uri: '0'
  editor_file_reference:
    id: editor_file_reference
    provider: editor
    status: false
    weight: -47
    settings: {  }
  filter_align:
    id: filter_align
    provider: filter
    status: false
    weight: -46
    settings: {  }
  filter_autop:
    id: filter_autop
    provider: filter
    status: false
    weight: -45
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: false
    weight: -44
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -49
    settings:
      allowed_html: '<br> <p> <h1 class="ckeditorheading"> <h2> <h4> <img src alt data-entity-type data-entity-uuid data-entity-substitution> <a accesskey id rel target title data-entity-type data-entity-uuid data-entity-substitution href> <drupal-url data-*> <button class> <drupal-media data-entity-substitution data-view-mode data-align data-caption title data-entity-type data-entity-uuid alt> <div id class="raw-html-embed"> <strong> <em> <blockquote> <ul> <ol> <li>'
      filter_html_help: false
      filter_html_nofollow: false
  filter_html_escape:
    id: filter_html_escape
    provider: filter
    status: false
    weight: -48
    settings: {  }
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: false
    weight: -41
    settings: {  }
  filter_htmlcorrector:
    id: filter_htmlcorrector
    provider: filter
    status: false
    weight: -40
    settings: {  }
  filter_image_lazy_load:
    id: filter_image_lazy_load
    provider: filter
    status: true
    weight: -46
    settings: {  }
  filter_pathologic:
    id: filter_pathologic
    provider: pathologic
    status: true
    weight: -37
    settings:
      settings_source: global
      local_settings:
        protocol_style: full
        local_paths: ''
  filter_url:
    id: filter_url
    provider: filter
    status: false
    weight: -43
    settings:
      filter_url_length: 72
  linkit:
    id: linkit
    provider: linkit
    status: true
    weight: -50
    settings:
      title: false
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: -50
    settings:
      default_view_mode: mhr_embedded_image
      allowed_view_modes:
        mhr_embedded_image: mhr_embedded_image
      allowed_media_types:
        image: image
  slick_filter:
    id: slick_filter
    provider: slick
    status: false
    weight: 4
    settings:
      optionset: default
      layout: ''
      skin: ''
      background: false
      loading: ''
      preload: false
      responsive_image_style: ''
      image_style: ''
      media_switch: ''
      ratio: ''
      thumbnail_style: ''
      overridables:
        arrows: '0'
        autoplay: '0'
        dots: '0'
        draggable: '0'
        infinite: '0'
        mouseWheel: '0'
        randomize: '0'
        variableWidth: '0'
      optionset_thumbnail: ''
      skin_thumbnail: ''
      thumbnail_caption: ''
      thumbnail_effect: ''
      thumbnail_position: ''
      override: false
  url_embed:
    id: url_embed
    provider: url_embed
    status: true
    weight: -38
    settings:
      enable_responsive: '1'
  url_embed_convert_links:
    id: url_embed_convert_links
    provider: url_embed
    status: true
    weight: -39
    settings:
      url_prefix: ''
