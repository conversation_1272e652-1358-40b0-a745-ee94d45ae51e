uuid: fecd5f42-56ae-4217-b947-40334ff3d950
langcode: en
status: true
dependencies:
  content:
    - 'block_content:basic:249fdeb1-392a-4ff8-91b5-c9966edca989'
  module:
    - block_content
    - node
  theme:
    - mhr
id: footerlogouk
theme: mhr
region: footer
weight: -17
provider: null
plugin: 'block_content:249fdeb1-392a-4ff8-91b5-c9966edca989'
settings:
  id: 'block_content:249fdeb1-392a-4ff8-91b5-c9966edca989'
  label: 'Footer Logo - UK'
  label_display: '0'
  provider: block_content
  status: true
  info: ''
  view_mode: full
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: true
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      form_page: form_page
