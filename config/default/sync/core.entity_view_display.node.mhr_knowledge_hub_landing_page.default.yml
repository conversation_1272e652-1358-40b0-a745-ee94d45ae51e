uuid: df18823c-e129-4f64-a075-d0e81a968e5b
langcode: en
status: true
dependencies:
  config:
    - field.field.node.mhr_knowledge_hub_landing_page.field_domain_access
    - field.field.node.mhr_knowledge_hub_landing_page.field_domain_all_affiliates
    - field.field.node.mhr_knowledge_hub_landing_page.field_domain_source
    - field.field.node.mhr_knowledge_hub_landing_page.field_exclude_from_search
    - field.field.node.mhr_knowledge_hub_landing_page.field_meta_description
    - field.field.node.mhr_knowledge_hub_landing_page.field_meta_image
    - field.field.node.mhr_knowledge_hub_landing_page.field_meta_tags
    - field.field.node.mhr_knowledge_hub_landing_page.field_mhr_title_for_kh_nav
    - field.field.node.mhr_knowledge_hub_landing_page.field_rows
    - field.field.node.mhr_knowledge_hub_landing_page.field_search_keywords
    - node.type.mhr_knowledge_hub_landing_page
  module:
    - user
id: node.mhr_knowledge_hub_landing_page.default
targetEntityType: node
bundle: mhr_knowledge_hub_landing_page
mode: default
content:
  content_moderation_control:
    settings: {  }
    third_party_settings: {  }
    weight: -20
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  field_exclude_from_search: true
  field_meta_description: true
  field_meta_image: true
  field_meta_tags: true
  field_mhr_title_for_kh_nav: true
  field_rows: true
  field_search_keywords: true
  langcode: true
  search_api_excerpt: true
