uuid: e8127a6f-d88e-451e-9a98-b3b8b530f375
langcode: en
status: true
dependencies:
  module:
    - domain
    - mhr_customizations
    - node
  theme:
    - mhr
id: phoneblock
theme: mhr
region: header_block_group_top_section
weight: -23
provider: null
plugin: phone_block
settings:
  id: phone_block
  label: 'Phone Block'
  label_display: '0'
  provider: mhr_customizations
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: false
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      article: article
      basic_page: basic_page
      blog_post: blog_post
      case_study: case_study
      download_listing_item: download_listing_item
      error_page: error_page
      event: event
      flexipage: flexipage
      form_page: form_page
      glossary: glossary
      knowledge_hub_page: knowledge_hub_page
      listing: listing
      pillar_page: pillar_page
      podcast_listing_item: podcast_listing_item
      product: product
      testimonial: testimonial
      video_listing_item: video_listing_item
      webinar: webinar
  domain:
    id: domain
    negate: false
    context_mapping:
      domain: '@domain.current_domain_context:domain'
    domains:
      uk: uk
      ie: ie
