uuid: b64f1704-9a40-46fa-89bd-aacac5e5e291
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content_hub_topic
    - node.type.article
    - taxonomy.vocabulary.topic
id: node.article.field_content_hub_topic
field_name: field_content_hub_topic
entity_type: node
bundle: article
label: Topic
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      topic: topic
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
