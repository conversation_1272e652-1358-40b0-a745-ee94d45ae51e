entity_type_groups:
  node:
    abm_page:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_qa_page: schema_qa_page
      schema_web_page: schema_web_page
    article:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_article: schema_article
    basic_page:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
    blog_post:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_article: schema_article
    error_page:
      basic: basic
    event:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_event: schema_event
    flexipage:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_qa_page: schema_qa_page
      schema_web_page: schema_web_page
      schema_web_site: schema_web_site
    listing:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
    product:
      basic: basic
      advanced: advanced
      open_graph_products: open_graph_products
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_product: schema_product
    testimonial:
      basic: basic
    webinar:
      basic: basic
      advanced: advanced
      open_graph: open_graph
      twitter_cards: twitter_cards
      schema_event: schema_event
separator: ','
tag_trim_method: beforeValue
use_maxlength: true
tag_trim_maxlength:
  metatag_maxlength_title: null
  metatag_maxlength_description: null
  metatag_maxlength_abstract: null
  metatag_maxlength_og_site_name: null
  metatag_maxlength_og_title: null
  metatag_maxlength_og_description: null
  metatag_maxlength_twitter_cards_title: null
  metatag_maxlength_twitter_cards_description: null
tag_scroll_max_height: ''
