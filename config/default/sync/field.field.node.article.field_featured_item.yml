uuid: 4e228603-044b-4ec6-a1f4-75308d608c38
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_featured_item
    - node.type.article
id: node.article.field_featured_item
field_name: field_featured_item
entity_type: node
bundle: article
label: 'Featured item'
description: "Note: only the most recently-published item set as 'featured' will be displayed as such."
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: Featured
  off_label: 'Not featured'
field_type: boolean
