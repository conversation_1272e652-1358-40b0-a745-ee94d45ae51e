uuid: e83f527f-51fc-4a25-9efe-659f276be67c
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_image
    - media.type.image
    - node.type.pillar_page
id: node.pillar_page.field_image
field_name: field_image
entity_type: node
bundle: pillar_page
label: Image
description: 'The recommended minimum image size is 1728px x 720px. The image will be scaled and cropped on delivery.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
