uuid: fbf7e9c5-dc0b-4252-9e4d-d29e62af14e4
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_mhr_partner_services
    - node.type.mhr_partner_integration
    - taxonomy.vocabulary.mhr_partner_services
id: node.mhr_partner_integration.field_mhr_partner_services
field_name: field_mhr_partner_services
entity_type: node
bundle: mhr_partner_integration
label: Services
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      mhr_partner_services: mhr_partner_services
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
