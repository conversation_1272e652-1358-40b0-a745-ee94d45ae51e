uuid: 99f0ce49-c46d-4db9-8565-8269e11ffcf0
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_featured_item
    - node.type.event
id: node.event.field_featured_item
field_name: field_featured_item
entity_type: node
bundle: event
label: 'Featured item'
description: "Note: only the most recently-published item set as 'featured' will be displayed as such."
required: false
translatable: true
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: Featured
  off_label: 'Not featured'
field_type: boolean
