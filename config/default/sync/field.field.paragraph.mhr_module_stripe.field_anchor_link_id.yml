uuid: 31218a79-1df6-4c45-9a1b-2c1e57c3d573
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_anchor_link_id
    - paragraphs.paragraphs_type.mhr_module_stripe
id: paragraph.mhr_module_stripe.field_anchor_link_id
field_name: field_anchor_link_id
entity_type: paragraph
bundle: mhr_module_stripe
label: 'Anchor Link ID'
description: "The anchor link #id for this block on the page.  This will allow you to link to parts within a page - e.g. /contact-us#contact-form\r\n<br/><b>Please use all lowercase alphanumeric/hyphens, with NO spaces or other punctuation - e.g. \"contact-form\" or \"request-brochure\" etc.<br/>"
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
