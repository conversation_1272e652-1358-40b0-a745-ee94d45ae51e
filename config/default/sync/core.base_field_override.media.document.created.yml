uuid: 1e89645d-62f1-4dc3-a4e6-fb7a3c1e0ff1
langcode: en
status: true
dependencies:
  config:
    - media.type.document
id: media.document.created
field_name: created
entity_type: media
bundle: document
label: 'Authored on'
description: 'The time the media item was created.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getRequestTime'
settings: {  }
field_type: created
