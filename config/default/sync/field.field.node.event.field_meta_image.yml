uuid: 919824c4-cc75-471b-ad4a-910eaf626e57
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_meta_image
    - media.type.image
    - node.type.event
id: node.event.field_meta_image
field_name: field_meta_image
entity_type: node
bundle: event
label: 'Sharing image'
description: 'Image size should be at least 1200x630px. Larger images will be scaled/cropped to that size.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
