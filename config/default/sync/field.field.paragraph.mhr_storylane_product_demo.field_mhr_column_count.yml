uuid: c007dce6-9b10-4f30-82e1-863bbab062c5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mhr_column_count
    - paragraphs.paragraphs_type.mhr_storylane_product_demo
  module:
    - options
id: paragraph.mhr_storylane_product_demo.field_mhr_column_count
field_name: field_mhr_column_count
entity_type: paragraph
bundle: mhr_storylane_product_demo
label: 'Column Count'
description: ''
required: true
translatable: false
default_value:
  -
    value: 1
default_value_callback: ''
settings: {  }
field_type: list_integer
