uuid: 25dba567-0b55-4e39-a075-7fd44dc46e2b
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content_hub_product
    - node.type.podcast_listing_item
    - taxonomy.vocabulary.products
id: node.podcast_listing_item.field_content_hub_product
field_name: field_content_hub_product
entity_type: node
bundle: podcast_listing_item
label: Product
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      products: products
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
