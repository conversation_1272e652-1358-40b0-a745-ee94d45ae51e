uuid: 775d1d56-8e8c-45de-807f-2b359dbda99d
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.image_link_grid.field_image_link_grid_rows
    - field.field.paragraph.image_link_grid.field_mhr_component_options
    - field.field.paragraph.image_link_grid.field_row_background_colour
    - paragraphs.paragraphs_type.image_link_grid
  module:
    - field_group
    - layout_paragraphs
    - paragraphs
third_party_settings:
  field_group:
    group_mhr_component_styling:
      children:
        - field_mhr_component_options
        - field_row_background_colour
      label: 'Component Styling'
      region: content
      parent_name: ''
      weight: 3
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
id: paragraph.image_link_grid.default
targetEntityType: paragraph
bundle: image_link_grid
mode: default
content:
  field_image_link_grid_rows:
    type: layout_paragraphs
    weight: 2
    region: content
    settings:
      preview_view_mode: default
      nesting_depth: 0
      require_layouts: 0
      empty_message: ''
    third_party_settings: {  }
  field_mhr_component_options:
    type: paragraphs
    weight: 0
    region: content
    settings:
      title: 'Component Options'
      title_plural: 'Component Options'
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: mhr_component_options
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_row_background_colour:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
