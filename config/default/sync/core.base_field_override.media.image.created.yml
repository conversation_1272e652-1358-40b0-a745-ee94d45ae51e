uuid: 70c1155f-7919-40d1-a969-f2354554ea14
langcode: en
status: true
dependencies:
  config:
    - media.type.image
id: media.image.created
field_name: created
entity_type: media
bundle: image
label: 'Authored on'
description: 'The time the media item was created.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getRequestTime'
settings: {  }
field_type: created
