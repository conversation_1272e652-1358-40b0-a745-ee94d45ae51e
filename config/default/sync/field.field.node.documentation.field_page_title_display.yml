uuid: 30ad4e3c-4da4-4e35-afa6-9cf15d9f58ad
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_page_title_display
    - node.type.documentation
  module:
    - options
id: node.documentation.field_page_title_display
field_name: field_page_title_display
entity_type: node
bundle: documentation
label: 'Page Title Display'
description: 'How to display the page title on the page.  If not shown then up to the content editor to ensure that an H1 is output appropriately on the page - e.g. via Hero Banner'
required: true
translatable: false
default_value:
  -
    value: output_h1
default_value_callback: ''
settings: {  }
field_type: list_string
