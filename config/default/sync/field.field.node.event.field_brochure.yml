uuid: 2a7bbcbb-b5c5-4c48-bb06-1de8e801b818
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_brochure
    - media.type.document
    - node.type.event
id: node.event.field_brochure
field_name: field_brochure
entity_type: node
bundle: event
label: 'Downloadable file'
description: 'Upload or use the media library to configure the downloadable file'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      document: document
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
