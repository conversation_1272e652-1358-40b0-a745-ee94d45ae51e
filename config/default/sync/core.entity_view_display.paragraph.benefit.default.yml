uuid: a077d4e6-1b73-4eb6-852f-fedc3e70e449
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.benefit.field_benefit_image
    - field.field.paragraph.benefit.field_benefit_text
    - field.field.paragraph.benefit.field_benefit_title
    - paragraphs.paragraphs_type.benefit
  module:
    - text
id: paragraph.benefit.default
targetEntityType: paragraph
bundle: benefit
mode: default
content:
  field_benefit_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: benefit
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_benefit_text:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_benefit_title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  search_api_excerpt: true
