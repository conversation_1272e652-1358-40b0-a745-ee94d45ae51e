uuid: 2ae3ac65-f703-40a3-89de-e6467ccb7a5f
langcode: en
status: true
dependencies:
  config:
    - field.field.media.svg.field_media_svg
    - media.type.svg
  module:
    - path
    - svg_image_field
id: media.svg.default
targetEntityType: media
bundle: svg
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_svg:
    type: svg_image_field_widget
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
      preview_image_max_width: 300
      preview_image_max_height: 300
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
