uuid: 68840a97-c448-458e-a0fb-5ca4fc447fb5
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.mhr_storylane_product_demo.field_domain_access
    - field.field.node.mhr_storylane_product_demo.field_domain_all_affiliates
    - field.field.node.mhr_storylane_product_demo.field_domain_source
    - field.field.node.mhr_storylane_product_demo.field_mhr_storylane_demo_embed
    - field.field.node.mhr_storylane_product_demo.field_mhr_storylane_text
    - field.field.node.mhr_storylane_product_demo.field_storylane_demo_type
    - node.type.mhr_storylane_product_demo
  module:
    - user
id: node.mhr_storylane_product_demo.teaser
targetEntityType: node
bundle: mhr_storylane_product_demo
mode: teaser
content:
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  field_mhr_storylane_demo_embed: true
  field_mhr_storylane_text: true
  field_storylane_demo_type: true
  langcode: true
  search_api_excerpt: true
