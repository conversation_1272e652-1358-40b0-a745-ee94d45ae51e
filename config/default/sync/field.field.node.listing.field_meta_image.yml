uuid: 19eb7173-d077-4882-b84c-1fbf989dde4d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_meta_image
    - media.type.image
    - node.type.listing
id: node.listing.field_meta_image
field_name: field_meta_image
entity_type: node
bundle: listing
label: 'Sharing image'
description: 'Image size should be at least 1200x630px. Larger images will be scaled/cropped to that size.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
