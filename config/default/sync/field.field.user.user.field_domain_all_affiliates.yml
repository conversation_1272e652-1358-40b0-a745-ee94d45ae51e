uuid: bc501792-e262-420a-b0e9-da97221d43d7
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_domain_all_affiliates
  module:
    - user
id: user.user.field_domain_all_affiliates
field_name: field_domain_all_affiliates
entity_type: user
bundle: user
label: 'Editor for all affiliates'
description: 'Make this user an editor on all domains.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
