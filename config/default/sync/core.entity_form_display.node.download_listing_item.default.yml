uuid: f1ba84dd-9e3c-4f4e-a86a-a9d08fd33ffc
langcode: en
status: true
dependencies:
  config:
    - entity_browser.browser.media_entity_browser
    - field.field.node.download_listing_item.body
    - field.field.node.download_listing_item.field_button
    - field.field.node.download_listing_item.field_content_hub_featured
    - field.field.node.download_listing_item.field_content_hub_product
    - field.field.node.download_listing_item.field_content_hub_resource
    - field.field.node.download_listing_item.field_content_hub_topic
    - field.field.node.download_listing_item.field_domain_access
    - field.field.node.download_listing_item.field_domain_all_affiliates
    - field.field.node.download_listing_item.field_domain_source
    - field.field.node.download_listing_item.field_download_tags
    - field.field.node.download_listing_item.field_heading
    - field.field.node.download_listing_item.field_published_date
    - field.field.node.download_listing_item.field_resource_image
    - field.field.node.download_listing_item.field_tags
    - node.type.download_listing_item
    - workflows.workflow.editorial
  module:
    - content_moderation
    - datetime
    - entity_browser
    - field_group
    - link
    - path
    - text
third_party_settings:
  field_group:
    group_content_hub:
      children:
        - field_content_hub_topic
        - field_content_hub_resource
        - field_content_hub_product
        - field_content_hub_featured
      label: 'Content Hub'
      region: content
      parent_name: ''
      weight: 18
      format_type: details_sidebar
      format_settings:
        classes: ''
        id: ''
        open: false
        description: ''
        required_fields: true
        weight: 0
id: node.download_listing_item.default
targetEntityType: node
bundle: download_listing_item
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 3
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_button:
    type: link_default
    weight: 4
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_content_hub_featured:
    type: boolean_checkbox
    weight: 24
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_content_hub_product:
    type: entity_reference_autocomplete
    weight: 22
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_content_hub_resource:
    type: entity_reference_autocomplete
    weight: 21
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_content_hub_topic:
    type: entity_reference_autocomplete
    weight: 20
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_domain_access:
    type: options_buttons
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  field_domain_all_affiliates:
    type: boolean_checkbox
    weight: 13
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_domain_source:
    type: options_select
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  field_heading:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_published_date:
    type: datetime_default
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_resource_image:
    type: entity_browser_entity_reference
    weight: 1
    region: content
    settings:
      entity_browser: media_entity_browser
      field_widget_display: label
      field_widget_edit: true
      field_widget_remove: true
      field_widget_replace: false
      open: true
      field_widget_display_settings: {  }
      selection_mode: selection_append
    third_party_settings: {  }
  field_tags:
    type: entity_reference_autocomplete
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 7
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 16
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 17
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 8
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  field_download_tags: true
  promote: true
  sticky: true
