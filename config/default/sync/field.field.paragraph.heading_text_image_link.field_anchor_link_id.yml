uuid: 8047a123-52d3-419a-b633-90bbd3c60ac5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_anchor_link_id
    - paragraphs.paragraphs_type.heading_text_image_link
id: paragraph.heading_text_image_link.field_anchor_link_id
field_name: field_anchor_link_id
entity_type: paragraph
bundle: heading_text_image_link
label: 'Anchor Link ID'
description: "The anchor link #id for this block on the page.  This will allow you to link to parts within a page - e.g. /contact-us#contact-form\r\n<br/><b>Please use all lowercase alphanumeric/hyphens, with NO spaces or other punctuation - e.g. \"contact-form\" or \"request-brochure\" etc.<br/>"
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
