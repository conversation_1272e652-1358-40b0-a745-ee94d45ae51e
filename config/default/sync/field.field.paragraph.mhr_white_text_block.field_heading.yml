uuid: ae004095-80bf-44b5-b52f-6bfe621e75e6
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_heading
    - paragraphs.paragraphs_type.mhr_white_text_block
id: paragraph.mhr_white_text_block.field_heading
field_name: field_heading
entity_type: paragraph
bundle: mhr_white_text_block
label: Heading
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
