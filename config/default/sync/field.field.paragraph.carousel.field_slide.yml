uuid: 10540243-41d2-4ac1-9f36-8abf9fee3df1
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_slide
    - paragraphs.paragraphs_type.carousel
    - paragraphs.paragraphs_type.carousel_slide
  module:
    - entity_reference_revisions
id: paragraph.carousel.field_slide
field_name: field_slide
entity_type: paragraph
bundle: carousel
label: Slide
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      carousel_slide: carousel_slide
    negate: 0
    target_bundles_drag_drop:
      bp_accordion:
        weight: 23
        enabled: false
      bp_accordion_section:
        weight: 24
        enabled: false
      bp_blank:
        weight: 25
        enabled: false
      bp_block:
        weight: 26
        enabled: false
      bp_carousel:
        weight: 27
        enabled: false
      bp_column_wrapper:
        weight: 28
        enabled: false
      bp_columns:
        weight: 29
        enabled: false
      bp_columns_three_uneven:
        weight: 30
        enabled: false
      bp_columns_two_uneven:
        weight: 31
        enabled: false
      bp_image:
        weight: 32
        enabled: false
      bp_modal:
        weight: 33
        enabled: false
      bp_simple:
        weight: 34
        enabled: false
      bp_tab_section:
        weight: 35
        enabled: false
      bp_tabs:
        weight: 36
        enabled: false
      bp_view:
        weight: 37
        enabled: false
      carousel:
        weight: 22
        enabled: false
      carousel_slide:
        weight: 39
        enabled: true
      from_library:
        weight: 38
        enabled: false
      grid:
        weight: 61
        enabled: false
      grid_box:
        weight: 62
        enabled: false
      hero_banner:
        weight: 39
        enabled: false
      image_and_text:
        weight: 40
        enabled: false
      mhr_blog_listings:
        weight: 65
        enabled: false
      mhr_faq_item:
        weight: 66
        enabled: false
      mhr_faqs:
        weight: 67
        enabled: false
      mhr_featured_blog:
        weight: 68
        enabled: false
      mhr_featured_news:
        weight: 69
        enabled: false
      mhr_grey_background_text:
        weight: 70
        enabled: false
      mhr_icon_and_link:
        weight: 71
        enabled: false
      mhr_icon_buttons:
        weight: 72
        enabled: false
      mhr_icon_buttons_item:
        weight: 73
        enabled: false
      mhr_latest_blog_articles:
        weight: 74
        enabled: false
      mhr_mini_slider_stripe:
        weight: 75
        enabled: false
      mhr_module_stripe:
        weight: 76
        enabled: false
      mhr_news_listings:
        weight: 77
        enabled: false
      mhr_product_introduction:
        weight: 78
        enabled: false
      mini_slider_item:
        weight: 79
        enabled: false
      module_item:
        weight: 80
        enabled: false
      paragraphs_base_container:
        weight: 41
        enabled: false
      paragraphs_base_text:
        weight: 42
        enabled: false
      product_details_and_features:
        weight: 83
        enabled: false
      product_feature:
        weight: 84
        enabled: false
field_type: entity_reference_revisions
