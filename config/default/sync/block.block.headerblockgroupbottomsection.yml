uuid: ae92ad1b-5b4e-459c-a36f-4b57d1b22b4a
langcode: en
status: true
dependencies:
  config:
    - blockgroup.block_group_content.header_block_group_bottom_section
  module:
    - blockgroup
    - node
  theme:
    - mhr
id: headerblockgroupbottomsection
theme: mhr
region: header_block_group_main_content
weight: -20
provider: null
plugin: 'block_group:header_block_group_bottom_section'
settings:
  id: 'block_group:header_block_group_bottom_section'
  label: 'Header Block Group Bottom Section'
  label_display: '0'
  provider: blockgroup
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: false
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      article: article
      basic_page: basic_page
      case_study: case_study
      download_listing_item: download_listing_item
      error_page: error_page
      event: event
      flexipage: flexipage
      form_page: form_page
      glossary: glossary
      knowledge_hub_page: knowledge_hub_page
      listing: listing
      podcast_listing_item: podcast_listing_item
      product: product
      testimonial: testimonial
      video_listing_item: video_listing_item
      webinar: webinar
      blog_post: blog_post
      pillar_page: pillar_page
