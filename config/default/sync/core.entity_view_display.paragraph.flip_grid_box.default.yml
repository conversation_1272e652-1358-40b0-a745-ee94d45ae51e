uuid: 1dc66f0e-7fd0-442e-8d46-1542877e5a78
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.flip_grid_box.field_flip_grid_box_body
    - field.field.paragraph.flip_grid_box.field_flip_grid_box_brand_name
    - field.field.paragraph.flip_grid_box.field_flip_grid_box_image
    - field.field.paragraph.flip_grid_box.field_flip_grid_box_link
    - paragraphs.paragraphs_type.flip_grid_box
  module:
    - field_group
    - link
    - text
third_party_settings:
  field_group:
    group_flip_grid_initial_view:
      children:
        - field_flip_grid_box_image
      label: 'Flip Grid Default'
      parent_name: ''
      region: content
      weight: 1
      format_type: html_element
      format_settings:
        classes: flip-grid-default
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
    group_flip_grid_hovered_view:
      children:
        - field_flip_grid_box_brand_name
        - field_flip_grid_box_body
        - field_flip_grid_box_link
      label: 'Flip <PERSON><PERSON> Hovered'
      parent_name: ''
      region: content
      weight: 2
      format_type: html_element
      format_settings:
        classes: flip-grid-hovered
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
id: paragraph.flip_grid_box.default
targetEntityType: paragraph
bundle: flip_grid_box
mode: default
content:
  field_flip_grid_box_body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 22
    region: content
  field_flip_grid_box_brand_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 21
    region: content
  field_flip_grid_box_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_flip_grid_box_link:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 23
    region: content
hidden:
  search_api_excerpt: true
