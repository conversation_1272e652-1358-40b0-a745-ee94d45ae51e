uuid: 497afc37-9e28-4f9b-bca1-97fb520d6469
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.content_hub_card_teaser
    - field.field.media.image.field_media_image
    - media.type.image
    - responsive_image.styles.content_hub_card_teaser
  module:
    - responsive_image
_core:
  default_config_hash: B1a2aBHmUulUZN6HrxITEH1tSResuVjXMBEv1lUBtOo
id: media.image.content_hub_card_teaser
targetEntityType: media
bundle: image
mode: content_hub_card_teaser
content:
  field_media_image:
    type: responsive_image
    label: visually_hidden
    settings:
      responsive_image_style: content_hub_card_teaser
      image_link: ''
      image_loading:
        attribute: eager
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  created: true
  langcode: true
  name: true
  search_api_excerpt: true
  thumbnail: true
  uid: true
