uuid: 5b7b1dac-758c-4a14-ac7c-2639c077d7e0
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mhr_tcc_card_image
    - media.type.image
    - paragraphs.paragraphs_type.mhr_teaser_card_cta_card
id: paragraph.mhr_teaser_card_cta_card.field_mhr_tcc_card_image
field_name: field_mhr_tcc_card_image
entity_type: paragraph
bundle: mhr_teaser_card_cta_card
label: Image
description: 'The image is output at 32:33 aspect ratio (i.e. almost square).'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
