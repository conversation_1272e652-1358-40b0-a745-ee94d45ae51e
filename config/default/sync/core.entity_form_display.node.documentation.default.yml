uuid: 77caf97f-4f2c-478c-af14-eb9afaaf4739
langcode: en
status: true
dependencies:
  config:
    - field.field.node.documentation.field_domain_access
    - field.field.node.documentation.field_domain_all_affiliates
    - field.field.node.documentation.field_domain_source
    - field.field.node.documentation.field_page_title_display
    - field.field.node.documentation.field_rows
    - field.field.node.documentation.field_top_page_content
    - node.type.documentation
  module:
    - content_moderation
    - layout_paragraphs
    - path
id: node.documentation.default
targetEntityType: node
bundle: documentation
mode: default
content:
  field_page_title_display:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_rows:
    type: layout_paragraphs
    weight: 3
    region: content
    settings:
      preview_view_mode: default
      nesting_depth: 0
      require_layouts: 0
      empty_message: ''
    third_party_settings: {  }
  field_top_page_content:
    type: layout_paragraphs
    weight: 2
    region: content
    settings:
      preview_view_mode: default
      nesting_depth: 0
      require_layouts: 0
      empty_message: 'Content to show at the top of the page - e.g. <PERSON>'
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  field_domain_access: true
  field_domain_all_affiliates: true
  field_domain_source: true
  langcode: true
  promote: true
  sticky: true
  uid: true
