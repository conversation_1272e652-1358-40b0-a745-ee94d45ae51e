This directory structure contains the settings and configuration files specific
to your site or sites and is an integral part of multisite configurations.

It is now recommended to place your custom and downloaded extensions in the
/modules, /themes, and /profiles directories located in the Drupal root. The
sites/all/ subdirectory structure, which was recommended in previous versions
of Drupal, is still supported.

See core/INSTALL.txt for information about single-site installation or
multisite configuration.
