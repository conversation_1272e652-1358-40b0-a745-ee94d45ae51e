/*
Override node edit pages so that edit area is full width.
Do this for claro or seven theme. Don't do it if using gin theme.
Only do this if browser is wide enough - note this is a max-width media query
 */
@media screen and (max-width: 1650px) {
  form.node-form .layout-node-form .layout-region {
      float: none;
      width: auto;
  }
}

form.node-form .layout-node-form .layout-region--node-main .layout-region__content,
form.node-form .layout-node-form .layout-region--node-footer .layout-region__content {
    max-width: none;
}

.gin--edit-form .page-wrapper__node-edit-form .block-local-tasks-block, .gin--edit-form .page-wrapper__node-edit-form .block-system-main-block, .gin--edit-form .page-wrapper__node-edit-form .messages-list, .gin--edit-form .page-wrapper__node-edit-form .node-form, .gin--edit-form .page-wrapper__node-edit-form .node-confirm-form, .gin--edit-form .page-wrapper__node-edit-form .admin-list, .gin--edit-form .page-wrapper__node-edit-form .help {
  max-width: 100% !important;
}


/*Prevent the edit icons from being obscured by background images and the like*/
.layout-paragraphs-actions {
    z-index: 1;
}
/*.layout-paragraphs-field .layout-paragraphs-actions,*/
/*.gu-mirror .layout-paragraphs-actions,*/
/*.layout-paragraphs-field .layout-controls {*/
/*    z-index: 9999;*/
/*}*/


.js-lpb-region .js-lpb-component,
.layout-paragraphs-layout-region .layout-paragraphs-item {
    width: 100%;
}

/*!* Hack for layout_paragraphs v2 *!*/
/*.lpb-dialog {*/
/*    height: 75%;*/
/*}*/

.vsc-gallery,
.vsc-carousel-grid,
.vsc-logo-carousel,
.paragraph--type--gallery-slider {
    display: grid;
    min-width: 0;
    overflow: hidden;
}

.lightgallery {
    gap: 3rem;
}

.vsc-gallery--footer {
    display: none;
}

.vsc-gallery .vsc-layout-inner,
.vsc-carousel-grid .vsc-layout-inner,
.vsc-logo-carousel .vsc-layout-inner,
.paragraph--type--gallery-slider > .slick {
    min-width: 0;
}

/* Make the component selector modal dialog take up multiple columns */
/* .ui-dialog Applies to any modal dialog  - such as:
    - component selector
    - media library dialog
    */
.ui-dialog {
    width: 95vw !important;
    height: auto !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    /*left: 50% !important;*/
    /*transform: translate(-50%, -50%);*/
}

/* Component selector - make it take up 4 columns */
.ui-dialog.lpb-dialog {
  max-width: 90rem !important;
}
/* media library dialog - give it as much space as possible */
.ui-dialog.media-library-widget-modal {
  width: 95vw !important;
  height: auto !important;
  /*top: 50px !important;*/
  max-width: 95vw !important;
  max-height: 95vh !important;
}

/* Make media library pop-up taller
 it needs an important because the height is inline from the admin theme */
.media-library-widget-modal .ui-dialog-content {
  height: 75vh !important;
}

/*Prevent webform modal from disappearing off top of screen */
.toolbar-tray-open.toolbar-horizontal .webform-ui-dialog {
  top: 50% !important
}

.node-form .entity-meta details[open] {
    background-image: none;
}

.node-form .entity-meta__header:first-child,
.node-form .entity-meta details:first-child {
    border-top-color: transparent;
}

/*#edit-meta {*/
/*  padding: 0;*/
/*}*/

/* Prevent massive svg icons */
svg.vsc-icon {
    max-height: 20px;
    max-width: 40px;
}
.vsc-icon-styled-list {
    display:none;
}

.lpb-component-list__group--layout {
    padding: 25px 0;
}
.lpb-component-list__group > .lpb-component-list__group--content {
    display: grid;
    grid-template-columns: 20% 20% 20% 20% 20%;
}
.lpb-component-list__group > div > div {
    padding: 10px;
}


/*Hide cookie widget*/
#ot-sdk-btn-floating {
  display: none;
}

/*Hide contextual edit buttons for images*/
.contextual button.focusable {
  display: none;
}

/* Space between elements */
.js-lpb-component-list >div {
  margin-bottom: 64px;
}

/* Ensure that the label on the layout paragraphs pop-over tab is readable */
.lpb-controls-label {
  color: #232429;
}

/* Add spaces between words in headings */
h2 .word {
  margin-right: .25em;
}

/* Make sure mini-slider arrows, and pluses to add more paragraphs, are always shown */
.paragraph--type--mhr-mini-slider-stripe,
.paragraph--type--image-link-grid-image-link {
  overflow: visible;
}

.paragraph--type--mhr-mini-slider-stripe .mini-slider__arrow--prev {
  right: auto;
}

.paragraph--type--mhr-mini-slider-stripe .mini-slider__arrow--next {
  left: 100%;
}

/* Show description on product slideshow */
.paragraph--type--slideshow .paragraph--type--slide {
  width: 100%;
}

/* Position FAQ chevron */
.faq-button-content .icn-chevron-down {
  position: relative;
}

.faq-button-content .icn-chevron-down::before {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Centre page number and chevron on medial library pop-up */
.media-library-content .pager .pager__items {
  align-items: center;
}

/* Put pager first/previous/next/last on same row as the numbers */
.pager .pager__items .pager__item--first a,
.pager .pager__items .pager__item--previous a,
.pager .pager__items .pager__item--next a,
.pager .pager__items .pager__item--last a {
  padding: 0;
}

.pager__item--first .pager__link::before,
.pager__item--previous .pager__link::before,
.pager__item--next .pager__link::after,
.pager__item--last .pager__link::after {
  transform: translateY(50%);
}

.pager .pager__items .pager__item a.is-active {
  color: white;
  background-color: #c8102e;
}

/* Make the links in text fields red */
.text-formatted a {
  color: #c8102e;
}

/* Make embedded webform labels white and links red */
.webform-submission-form label {
  color: #fff;
}

.webform-submission-form a {
  color: #c8102e;
}

/* Make testimonial slider items show up */
.paragraph--type--carousel .carousel__item:first-of-type {
  opacity: 1;
}

/* Stop sliders from adding a horizontal scrollbar */
.layout-container main {
  overflow-x: clip;
}

/* Stop things from animating in */
.paragraph--type--_-column-text .text-left,
.paragraph--type--_-column-text .text-right,
.paragraph--type--mhr-webform .column--text,
.paragraph--type--mhr-webform .column--form,
.paragraph--type--image-and-text.alignment-imageright .column--text,
.paragraph--type--image-and-text.alignment-imageleft .column--text,
.paragraph--type--image-and-text.alignment-imageright .column--image,
.paragraph--type--image-and-text.alignment-imageleft .column--image,
.paragraph--type--mhr-module-stripe .paragraph--type--module-item .field--name-field-module-title h4 {
  transform: translateX(0);
  opacity: 1;
}

.paragraph--type--grid .field--name-field-grid-box > div,
.paragraph--type--grid .field--name-field-grid-box > a,
.paragraph--type--carousel,
.paragraph--type--mhr-grey-background-text .grey-block,
.paragraph--type--mhr-mini-slider-stripe.animate .mini-slider__item,
.paragraph--type--mhr-mini-slider-stripe .mini-slider__item,
.paragraph--type--full-width-cta,
.paragraph--type--mhr-icon-buttons .field--name-field-button-item .field__item,
.paragraph--type--mhr-module-stripe .paragraph--type--module-item p,
.paragraph--type--latest-downloads .view-mhr-latest-download-items .view-content > div,
.paragraph--type--latest-downloads .view-mhr-latest-download-items .view-content > a,
.a11y-paragraphs-tabs__wrapper,
.paragraph--type--mhr-latest-blog-articles .latest-blog-articles > div,
.paragraph--type--mhr-latest-blog-articles .latest-blog-articles > a,
.paragraph--type--mhr-faqs .paragraph--type--mhr-faq-item {
  transform: translateY(0);
  opacity: 1;
}

/* Don't show the red bits on buttons */
.field--name-field-button[id^="edit-field-button-wrapper"]::before {
    display: none;
}

/* Nor the grey background */
.field--name-field-button[id^="edit-field-button-wrapper"] {
    background: transparent;
}

/* And don't make the button text white on hover */
.field--name-field-button:hover span {
  color: #0036b1;
}

/* Stop focus states from going off the left of the screen */
.block-system-main-block {
  margin-left: 5px;
}

/* Add rounded corners */
img {
  border-radius: 0.75rem;
}

/* HELPDESK-5207 - Workaround for bug in CKeditor 5 that causes link button panel to be hidden behind dialog window */

.ck.ck-balloon-panel {
  z-index: 1300 !important;
}

/* Override Storylane CSS to prevent overlay embed output overlapping site headers and navigation in Drupal. */
.sl-embed-container {
  z-index: 1;
}
