global-styling:
  css:
    component:
      libraries/swiper-bundle.min.css: {attributes: {defer: false, async: false} }
      /libraries/jquery.select2/dist/css/select2.min.css: { minified: true }
      dist/css/main.min.css: {attributes: {defer: false, async: false} }

  js:
    libraries/swiper-bundle.min.js: {attributes: {defer: false} }
    https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js: {type: external, attributes: {defer: false, async: false} }
    https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js: {type: external, attributes: {defer: false, async: false} }
    libraries/jquery.touchSwipe.min.js: {attributes: {defer: true} }
    libraries/select2.min.js: {attributes: {defer: false} }
    /libraries/jquery.select2/dist/js/select2.min.js: { minified: true }
    dist/js/main.min.js: {}

  dependencies:
    - core/drupal
    - core/once
    - core/jquery
    - mhr/lodash-custom

lodash-custom:
  js:
    dist/js/separate-delivery/lodash-custom.min.js: {}

fonts:
  css:
    theme:
      https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap: { type: external, attributes: { defer: false, async: false } }

vsc_accordion:
  css:
    theme:
      dist/copy/versantus-structured-content-accordion.css: {}
  js:
    dist/js/2023-mhr-scripts-separate-delivery/versantus-structured-content-accordion.js: {attributes: {defer: false} }

  dependencies:
    - core/drupal
    - core/once
    - core/jquery

mhr-carousel:
  css:
    component:
      libraries/swiper-bundle.min.css: {attributes: {defer: false, async: false} }
  js:
    libraries/swiper-bundle.min.js: {attributes: {defer: false} }
    dist/js/separate-delivery/mhr-structured-content-carousel.min.js: {attributes: {defer: true}}

  dependencies:
    - core/drupal
    - core/once
    - core/jquery
    - mhr/lodash-custom

mhr-hero-banner:
  js:
    dist/js/separate-delivery/vits-hero-banner-video.js: {attributes: {defer: true}}

mhr-tabs:
  css:
    component:
      libraries/swiper-bundle.min.css: {attributes: {defer: false, async: false} }
  js:
    libraries/swiper-bundle.min.js: {attributes: {defer: false} }
    dist/js/2023-mhr-scripts-separate-delivery/mhr-tabs.min.js: {attributes: {defer: true}}

  dependencies:
    - core/drupal
    - core/once
    - core/drupalSettings

mhr-partners-integrations:
  css:
    component:
      libraries/choices.min.css: {attributes: {defer: false, async: false} }
  js:
    libraries/choices.min.js: {attributes: {defer: true}}
    dist/js/separate-delivery/mhr-partners-integrations.min.js: {attributes: {defer: true}}
