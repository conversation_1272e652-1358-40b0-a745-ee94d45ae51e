(function (Drupal, drupalSettings, once) {

  Drupal.behaviors.mhrTabs = {

    attach: function (context, settings) {

      function setUpTabs(element, index) {

        // Create swiper variable.
        let swiper;
        
        // If there is not a swiper then create it.
        if (swiper === undefined) {
          initSwiper();
        }
        
        // Swiper init function.
        function initSwiper() {

          // Check the paragraph ID output as a data attribute on the component wrapper,
          // to make sure the correct tab labels are added, and to be used when adding a
          // unique class for Swiper to hook onto.
          let containerWrapperElement = element.closest('.vits-tabs');
          let tabsParagraphId = containerWrapperElement.dataset.mhrTabsId;

          // Add unique class to each tabs component for Swiper to hook onto.
          let uniqueClass = 'mhr-tabs-' + tabsParagraphId;
          element.classList.add(uniqueClass);

          // Set up Swiper settings.
          let swiperSettings = {
            autoHeight: true,
            navigation: false,
            pagination: {
              clickable: true,
              el: '.swiper-pagination',
              renderBullet: function (index, className) {
                return '<span class="vits-tabs__tab-title ' + className + '">' + drupalSettings.mhr.tabLabels[tabsParagraphId][index].tabs_panel_title + "</span>";
              },
            },
            slidesPerView: 1,
            spaceBetween: 24,
            speed: 350,
            watchSlidesProgress: true
          };

          // Initialise Swiper.
          swiper = new Swiper('.' + uniqueClass, swiperSettings);
        }
      }

      // Instantiate Swiper functionality to create tabs interface.
      once('mhrTabs', '.vits-tabs__panels .swiper', context).forEach(function (element, index) {
        setUpTabs(element, index);
      });
    }
  }

} (Drupal, drupalSettings, once));
