/*--------------------------------------------------------------
    GSAP - IMAGE SURROUND CTA
--------------------------------------------------------------*/

(function ($, Drupal, drupalSettings, once) {

  Drupal.behaviors.vscGsapImageSurroundCTA = {

    attach: function (context, settings) {

      const setUpImageSurroundCTA = (element) => {

        const setTweensToTimeline = (imageSurroundCtaImages) => {

          imageSurroundCtaImages.forEach(function (imageSurroundCtaImageItem) {

            const imageAnimationParallaxValue = imageSurroundCtaImageItem.getAttribute('data-animation-parallax');
            let yPercentValue = -50;
            if (imageAnimationParallaxValue && !isNaN(parseInt(imageAnimationParallaxValue, 10))) {
              yPercentValue = parseFloat(imageAnimationParallaxValue);
            };

            const dataAnimationScaleValue = imageSurroundCtaImageItem.getAttribute('data-animation-scale');
            let scaleValue = 1;
            if (dataAnimationScaleValue && !isNaN(parseInt(dataAnimationScaleValue, 10))) {
              scaleValue = parseFloat(dataAnimationScaleValue);
            };

            tl.to(imageSurroundCtaImageItem, {
              yPercent: yPercentValue,
              scale: scaleValue,
              ease: "none"
            }, '<');
          });
        }

        const imageSurroundCtaImages = element.querySelectorAll('.image-surround-cta__image');

        const tl = gsap.timeline({
          scrollTrigger: {
            invalidateOnRefresh: true,
            trigger: element,
            start: "top bottom",
            end: "bottom top",
            scrub: true
          }
        });

        if (imageSurroundCtaImages) {
          setTweensToTimeline(imageSurroundCtaImages);
        }
      }

      // Instantiate Image Surround CTA functionality
      once('vscGsapImageSurroundCTA', '.image-surround-cta', context).forEach(function (element) {

        setUpImageSurroundCTA(element);
      });
    }
  }

} (jQuery, Drupal, drupalSettings, once));
