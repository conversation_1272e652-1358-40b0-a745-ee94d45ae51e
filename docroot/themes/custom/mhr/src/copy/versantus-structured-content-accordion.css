/* Accordion outer wrapper */
.vsc-accordion {
  width: 100%;
}

/* Accordions List Container */
dl.vsc-accordion-container {
  margin: 0;
}

/* Accordion Item Wrapper */

.vsc-accordion-content-container {
  margin-bottom: 16px;
}

.vsc-accordion-content-container:last-child {
  margin-bottom: 0;
}

/* Accordion Open Class Modifier */
.vsc-accordion-content-container.accordion_open .accordion-icon-main {
  transform: rotate(180deg);
}

/* Main Accordion Icon */
svg.accordion-icon-main {
  fill: #333;
  transition: transform 0.3s ease-in-out;
}

/* Accordion Title Button Wrapper */
.vsc-accordion-button {
  position: relative;
  display: block;
  width: 100%;
  text-align: left;
  border: 1px solid #ccc;
  padding: 20px 48px 20px 20px;
  box-sizing: border-box;
  appearance: none;
  cursor: pointer;
}

/* Open Close Icons Wrapper */

.vsc-accordion-icons {
  position: absolute;
  top: 50%;
  right: 20px;
  width: 24px;
  height: 24px;
  transform: translateY(-50%);
}

/* Accordion Content Wrapper */
dd.vsc-accordion-content {
  padding: 20px;
  overflow-y: hidden;
}

/* Accordion Initialised Class Modifier */
.vsc-accordion-content.accordion_initialised {
  display: none;
}
