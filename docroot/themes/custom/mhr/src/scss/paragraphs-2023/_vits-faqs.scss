/* --------------------------------------------------------------
    VITS PARAGRAPH - FAQs
-------------------------------------------------------------- */

// Main container
.vits-faqs {
  > * {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: $spacer--15;
  }

  // Override VSC component CSS
  .vsc-accordion-button {
    border: 0;
    padding: 0;
  }

  svg.accordion-icon-main {
    fill: none;
  }

  dd.vsc-accordion-content {
    padding: $spacer--5 0 0;
  }
}

// FAQs header
.vsc-accordion--header {
  max-width: 600px;
  text-align: center;

  h2 {
    margin-bottom: 0;
  }
}

// FAQs subheading
h3.vits-faqs__subheading {
  margin-bottom: 0;
}

// FAQs content
.vsc-accordion-container {
  background-color: $palette--white;
  border-radius: $border-radius--card;
  box-sizing: border-box;
  margin: 0;
  padding: $spacer--10 $spacer--5;
  width: 100%;
}

// FAQs item container
.vsc-accordion-content-container {
  &:not(:last-of-type) {
    border-bottom: 1px solid $palette--mid-grey;
    margin-bottom: $spacer--6;
    padding-bottom: $spacer--6;
  }
}

// FAQs item trigger containing title and icon
.vsc-accordion-button {
  display: flex;
  justify-content: space-between;

  // FAQs item trigger icon container
  .vsc-accordion-icons {
    border: 1px solid $palette--black;
    border-radius: $spacer--1;
    display: grid;
    height: $spacer--8;
    place-items: center;
    right: 0;
    width: $spacer--8;
  }
}

// FAQs item title
.vsc-accordion-title {
  margin-bottom: 0;
  padding-right: calc(#{$spacer--8} + #{$spacer--6});
}

// Text colouring, including overrides if using a dark background colour to prevent
// the normal background colours being used.
.vits-faqs,
.vits-faqs.vsc-dark-background {
  // Overrides when FAQ item open.
  .accordion_open {
    .vsc-accordion-title {
      color: $palette--red;
    }

    .accordion-icon-main {
      transform: rotate(180deg);
    }
  }

  .vsc-accordion-button:focus,
  .vsc-accordion-button:hover {
    .vsc-accordion-title {
      color: $palette--red;
    }
  }

  // FAQs subheading
  h3.vits-faqs__subheading {
    color: $palette--text-primary;
  }

  // FAQs item title
  .vsc-accordion-title {
    color: $palette--text-primary;
  }

  // FAQs item text
  .vsc-accordion-content {
    p,
    a,
    ul,
    ol {
      color: $palette--black;
    }

    a:hover {
      color: $palette--red;
    }
  }
}

// Breakpoint Styles - Tablet Smaller
@media screen and (min-width: $breakpoint--tablet-smaller) {
  // FAQs content
  .vsc-accordion-container {
    padding: $spacer--20;
  }

  // FAQs item trigger icon container
  .vsc-accordion-button {
    .vsc-accordion-icons {
      height: $spacer--11;
      width: $spacer--11;
    }
  }

  // FAQs item title
  .vsc-accordion-title {
    padding-right: calc(#{$spacer--11} + #{$spacer--6});
  }
}
