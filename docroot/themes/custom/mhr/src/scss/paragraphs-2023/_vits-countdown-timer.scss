/* --------------------------------------------------------------
    VITS PARAGRAPH - COUNTDOWN TIMER
-------------------------------------------------------------- */

// Outer wrapper
.vits-countdown-timer {
  // Timer Ended modifier class
  &.vits-countdown-timer--ended {
    .countdown-time,
    .prefix-text,
    .suffix-text {
      display: none;
    }

    .countdown-timer-ended-message {
      display: block;
    }

    .countdown-timer-ended-text {
      margin-block: $spacer--8;
      text-align: center;
    }
  }

  // Timer layout container
  .countdown-timer-container {
    align-items: center;
    display: flex;
    flex-direction: column;
  }

  .prefix-text {
    margin-bottom: $spacer--6;
    text-align: center;
  }

  // Timer component
  .countdown-time {
    align-items: center;
    display: flex;
    justify-content: center;
    margin-inline: auto;
    max-width: 420px;
    width: 100%;
  }

  .interval-separator {
    flex-shrink: 0;
    margin-inline: $spacer--1;

    img {
      display: block;
      width: 4px;
    }
  }

  .interval-container {
    align-items: center;
    aspect-ratio: 1;
    background-color: $palette--blue;
    border-radius: $border-radius--large;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-block: $spacer--2;
    width: 100%;
  }

  .countdown-value {
    color: $palette--text-dark-background;
    font-family: $font-family--secondary;
    font-size: rem-calc(24px);
    letter-spacing: $letter-spacing--extra-narrow;
    line-height: 1.2;
    margin: unset;
    text-align: center;
  }

  .countdown-label {
    color: $palette--text-dark-background;
    font-size: $font-size--base;
    line-height: 1.2;
    margin: unset;
    text-align: center;

    // Full label text character fragment
    span {
      display: none;
    }
  }

  .countdown-days-label,
  .countdown-hours-label,
  .countdown-minutes-label {
    text-transform: capitalize;
  }

  .suffix-text {
    color: $palette--text-primary;
    margin: $spacer--6 0 0;
    text-align: center;
  }

  .countdown-timer-ended-message {
    display: none;
  }

  // Breakpoint Styles - Tablet
  @media screen and (min-width: $breakpoint--tablet) {
    // Timer component
    .countdown-time {
      max-width: 720px;
    }

    .interval-container {
      border-radius: $border-radius--xl;
    }

    .interval-separator {
      margin-inline: $spacer--2;

      img {
        width: 8px;
      }
    }

    .countdown-value {
      font-size: rem-calc(48px);
    }

    .countdown-label {
      font-size: rem-calc(20px);

      // Full label text character fragment
      span {
        display: inline;
      }
    }

    .countdown-seconds-label {
      text-transform: capitalize;
    }
  }

  // Breakpoint Styles - Desktop Large
  @media screen and (min-width: $breakpoint--desktop-large) {
    // Timer component
    .countdown-time {
      max-width: 920px;
    }

    .interval-container {
      border-radius: $border-radius--card;
    }

    .countdown-value {
      font-size: rem-calc(60px);
    }

    .countdown-label {
      font-size: rem-calc(24px);
    }
  }
}
