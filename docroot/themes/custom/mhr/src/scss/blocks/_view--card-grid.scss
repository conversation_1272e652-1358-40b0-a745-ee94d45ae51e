/* --------------------------------------------------------------
    VIEW - CARD GRID
-------------------------------------------------------------- */

// View Card Grid - Filters Container

.vsc-view-card-grid__filters {
  margin-bottom: $spacer--8;
}

// View Card Grid - Filters - Inner Content
.vsc-view-card-grid__filters-inner-content {
  display: grid;
  row-gap: $spacer--5;
}

// View Card Grid - Content Container

.vsc-view-card-grid__content {
  display: grid;
  row-gap: $spacer--5;
}

// Breakpoint Styles - Tablet
@media screen and (min-width: $breakpoint--tablet) {
  // View Card Grid - Content Container

  .vsc-view-card-grid__content {
    display: grid;
    gap: $spacer--6;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
  }
}

// Breakpoint Styles - Desktop Large
@media screen and (min-width: $breakpoint--desktop-large) {
  // View Card Grid - Content Container

  .vsc-view-card-grid__content {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto;
  }
}

/* --------------------------------------------------------------
    VIEW _ CARD GRID - MARKUP EXAMPLE
-------------------------------------------------------------- */

/*
<div class="vsc-view-card-grid">

  <div class="vsc-view-card-grid__filters">
    <div class="vsc-view-card-grid__filters-inner-content">

    </div>
  </div>

  <div class="vsc-view-card-grid__content">

  </div>
</div>
*/
