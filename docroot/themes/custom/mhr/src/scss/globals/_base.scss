/* --------------------------------------------------------------
    BASE STYLES
-------------------------------------------------------------- */

/*
   === Base Styles ===
*/

/* fullscreen setup */

html,
body {
  height: 100%;
}

// Make sure embeds and iframes fit their containers

embed,
iframe {
  max-width: 100%;
}

img {
  height: auto;
  max-width: 100%;
}

// Default focus-visible styling
:focus-visible {
  outline: $palette--blue auto 1px;
}

// Hidden elements class

.sr-only {
  clip: rect(1px, 1px, 1px, 1px); /* Clip so that it doesn't show */
  height: 1px; /* Nearly collapsed */
  overflow: hidden;
  position: absolute; /* Outside the DOM flow */
  width: 1px;
}

// Smooth scrolling

html {
  scroll-behavior: smooth;
}

@media screen and (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
