@use 'sass:math';

/*
Removes the unit (e.g. px, em, rem) from a value, returning the number only.
@param {Number} $num - Number to strip unit from.
@returns {Number} The same number, sans unit.
*/
@function strip-unit($num) {
  @return math.div($num, $num * 0 + 1);
}

/*
Converts a pixel value to matching rem value. *Any* value passed, regardless of unit, is assumed to be a pixel value.
By default, the base pixel value used to calculate the rem value is taken from the `$global-font-size` variable.
@access private
@param {Number} $value - Pixel value to convert.
@param {Number} $global-font-size [null] - Base for pixel conversion.
@returns {Number} A number in rems, calculated based on the given value.
rem values are passed through as is.
*/
@function rem-calc($value) {
  /* Turn 0rem into 0 */
  @if $value == 0 {
    $value: 0;
  }

  /* Calculate rem if units for $value is not rem */
  @else if unit($value) != 'rem' {
    $value: #{math.div(strip-unit($value), strip-unit($global-font-size))}rem;
  }

  @return $value;
}
