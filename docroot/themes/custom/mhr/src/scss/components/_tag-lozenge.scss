/* --------------------------------------------------------------
    TAG LOZENGE
-------------------------------------------------------------- */

/*
   === Main Tag ===
*/

// Outer wrapper
.tag-lozenge {
  background-color: transparent;
  border: 1px solid $palette--blue;
  border-radius: $border-radius--small;
  box-sizing: border-box;
  color: $palette--blue;
  display: inline-block;
  font-size: rem-calc(12px);
  font-weight: 600;
  justify-content: center;
  letter-spacing: $letter-spacing--narrow;
  line-height: $line-height--button;
  padding: $spacer--1 $spacer--3;
  text-align: center;
  text-transform: uppercase;
  text-wrap: pretty;

  &:focus {
    background-color: $palette--blue;
    border-color: $palette--blue;
    color: $palette--white;
  }

  @media screen and (hover) {
    transition:
      color 0.3s ease-in-out,
      background-color 0.3s ease-in-out,
      border-color 0.3s ease-in-out;

    &:hover {
      background-color: $palette--blue;
      border-color: $palette--blue;
      color: $palette--white;
      text-decoration: none;
    }
  }
}

/*
   === Card Overlay Tag ===
*/

// Outer wrapper
.tag-lozenge-card-overlay {
  background-color: $palette--blue;
  border-radius: 9999px;
  box-sizing: border-box;
  color: $palette--white;
  display: inline-block;
  font-size: rem-calc(12px);
  font-weight: 600;
  justify-content: center;
  letter-spacing: $letter-spacing--narrow;
  line-height: $line-height--button;
  padding: $spacer--1 $spacer--3;
  text-align: center;
  text-transform: uppercase;
  text-wrap: pretty;
}
