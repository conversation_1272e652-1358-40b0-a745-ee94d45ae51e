.vits-featured-article {
  align-items: center;
}

.vits-featured-article__sub-heading {
  color: $palette--red;
  margin-bottom: $spacer--3;
  text-transform: uppercase;
}

.vits-featured-article__heading {
  color: $palette--text-primary;
  font-family: $font-family--primary;
  font-weight: 600;
}

.vits-featured-article__image {
  img {
    border-radius: $border-radius--xl;
    display: block;
    height: auto;
    width: 100%;
  }
}
