/* --------------------------------------------------------------
    HEADER DOMAIN SELECTOR
-------------------------------------------------------------- */

// Outer wrapper
.header-domain-selector {
  position: relative;

  // Open modifier class
  &.header-domain-selector--open {
    // Select container
    .header-domain-selector__select {
      background-color: $palette--mid-grey-50;
    }

    // Select icon
    .header-domain-selector__select-icon {
      rotate: 180deg;
    }

    // Dropdown container
    ul.header-domain-selector__dropdown {
      opacity: 1;
      visibility: visible;
    }
  }
}

// Select container
.header-domain-selector__select {
  align-items: center;
  border-radius: $border-radius--small;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  gap: $spacer--2;
  min-width: 136px; // To contain the longest text; if text changes this may need to be altered.
  padding: rem-calc(15px) $spacer--2;

  &:focus {
    background-color: $palette--mid-grey-50;
  }
}

// Domain info
.header-domain-selector__domain-info {
  align-items: center;
  display: flex;
  flex-grow: 1;
  gap: $spacer--1;
}

// Domain info text
.header-domain-selector__domain-info-text {
  font-size: rem-calc(12px);
}

// Select icon
.header-domain-selector__select-icon {
  transition: rotate 0.2s ease-in-out;
}

// Dropdown list
ul.header-domain-selector__dropdown {
  background-color: $palette--mid-grey;
  border: 1px solid $palette--mid-grey-50;
  border-radius: $border-radius--small;
  left: 50%;
  min-width: 90px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  top: calc(100% + $spacer--1);
  transform: translateX(-50%);
  visibility: hidden;
  width: 100%;
}

// Dropdown list item
li.header-domain-selector__dropdown-item {
  // Active modifier class
  &.header-domain-selector__dropdown-item--active {
    // Selected icon
    .header-domain-selector__dropdown-item-selected-icon {
      opacity: 1;
    }
  }

  a {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    font-size: rem-calc(12px);
    gap: $spacer--1;
    padding: rem-calc(10px) $spacer--2;

    &:focus {
      background-color: $palette--blue;
      color: $palette--white;

      // Selected icon
      .header-domain-selector__dropdown-item-selected-icon {
        stroke: $palette--white;
      }
    }
  }
}

// Dropdown list item - Selected icon
.header-domain-selector__dropdown-item-selected-icon {
  opacity: 0;
}

// Hover
@media screen and (hover) {
  // Select container
  .header-domain-selector__select {
    transition: background-color 0.3s ease-in-out;

    &:hover {
      background-color: $palette--mid-grey-50;
      transition: background-color 0.15s ease-in-out;
    }
  }

  // Dropdown list item
  li.header-domain-selector__dropdown-item {
    a {
      &:hover {
        background-color: $palette--blue;
        color: $palette--white;

        // Selected icon
        .header-domain-selector__dropdown-item-selected-icon {
          stroke: $palette--white;
        }
      }
    }
  }
}

/* --------------------------------------------------
    OVERLAY CONTENT OVERRIDES
-------------------------------------------------- */

// Overlay Content modifier class
.overlay--header-navbar-main {
  // Header element wrapper

  header.navbar-main:not(:focus, :focus-within, :hover) {
    // Domain info text
    .header-domain-selector__domain-info-text {
      transition: color 0.2s ease-in-out;
    }

    // Select icon
    .header-domain-selector__select-icon {
      transition:
        rotate 0.2s ease-in-out,
        stroke 0.2s ease-in-out;
    }

    // Globe icon
    .header-domain-selector__globe-icon {
      transition: stroke 0.2s ease-in-out;
    }
  }
}

// Breakpoint Styles - MAX WIDTH - Desktop Large Max
@media screen and (max-width: $breakpoint--desktop-large-max) {
  .overlay--header-navbar-main:not(.sticky--header-navbar-main) {
    // Header element wrapper

    header.navbar-main {
      // Outer wrapper
      .header-domain-selector:not(.header-domain-selector--open) {
        // Select container
        .header-domain-selector__select {
          border-color: $palette--white;
          transition:
            background-color 0.2s ease-in-out,
            border-color 0.2s ease-in-out;

          &:focus,
          &:hover {
            // Domain info text
            .header-domain-selector__domain-info-text {
              color: $palette--blue;
            }

            // Select and Globe icons
            .header-domain-selector__select-icon,
            .header-domain-selector__globe-icon {
              stroke: $palette--blue;
            }
          }
        }

        // Domain info text
        .header-domain-selector__domain-info-text {
          color: $palette--white;
        }

        // Select and Globe icons
        .header-domain-selector__select-icon,
        .header-domain-selector__globe-icon {
          stroke: $palette--white;
        }
      }
    }
  }
}

// Breakpoint Styles - Desktop Nav Switch
@media screen and (min-width: $breakpoint--desktop-nav-switch) {
  .overlay--header-navbar-main:not(.sticky--header-navbar-main) {
    // Header element wrapper

    header.navbar-main:not(:focus, :focus-within, :hover) {
      // Outer wrapper
      .header-domain-selector:not(.header-domain-selector--open) {
        // Select container
        .header-domain-selector__select {
          border-color: $palette--white;
          transition:
            background-color 0.2s ease-in-out,
            border-color 0.2s ease-in-out;
        }

        // Domain info text
        .header-domain-selector__domain-info-text {
          color: $palette--white;
        }

        // Select and Globe icons
        .header-domain-selector__select-icon,
        .header-domain-selector__globe-icon {
          stroke: $palette--white;
        }
      }
    }
  }
}
