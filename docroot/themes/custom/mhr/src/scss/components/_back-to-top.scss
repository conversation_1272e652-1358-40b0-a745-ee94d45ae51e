/* --------------------------------------------------------------
    BACK TO TOP
-------------------------------------------------------------- */

.mhr-back-to-top {
  bottom: $spacer--6;
  opacity: 0;
  pointer-events: none;
  position: fixed;
  right: $spacer--6;
  transition: opacity linear 0.15s;
  z-index: 1;

  button {
    background-color: $palette--white;
  }
}

.mhr-back-to-top__visible {
  opacity: 1;
  pointer-events: all;
}

@media screen and (min-width: $breakpoint--desktop) {
  .mhr-back-to-top {
    bottom: $spacer--12;
    right: $spacer--12;
  }
}
