{#
/**
 * @file
 * Default theme implementation to display a paragraph.
 *
 * Available variables:
 * - paragraph: Full paragraph entity.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - paragraph.getCreatedTime() will return the paragraph creation timestamp.
 *   - paragraph.id(): The paragraph ID.
 *   - paragraph.bundle(): The type of the paragraph, for example, "image" or "text".
 *   - paragraph.getOwnerId(): The user ID of the paragraph author.
 *   See Drupal\paragraphs\Entity\Paragraph for a full list of public properties
 *   and methods for the paragraph object.
 * - content: All paragraph items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - paragraphs: The current template type (also known as a "theming hook").
 *   - paragraphs--type-[type]: The current paragraphs type. For example, if the paragraph is an
 *     "Image" it would result in "paragraphs--type--image". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - paragraphs--view-mode--[view_mode]: The View Mode of the paragraph; for example, a
 *     preview would result in: "paragraphs--view-mode--preview", and
 *     default: "paragraphs--view-mode--default".
 * - view_mode: View mode; for example, "preview" or "full".
 * - logged_in: Flag for authenticated user status. Will be true when the
 *   current user is a logged-in member.
 * - is_admin: Flag for admin user status. Will be true when the current user
 *   is an administrator.
 *
 * @see template_preprocess_paragraph()
 *
 * @ingroup themeable
 */
#}

{% set buttonStyle = content.field_button_style[0]['#markup']|default('outline')|lower %}
{% set buttonClass = 'vsc-button-item-outline' %}

{% if buttonStyle == 'solid' %}
  {% set buttonClass = 'vsc-button-item-solid' %}
{% elseif buttonStyle == 'outline' %}
  {% set buttonClass = 'vsc-button-item-outline' %}
{% elseif buttonStyle == 'solid-light' %}
  {% set buttonClass = 'vsc-button-item-outline-light' %}
{% endif %}

{% set classes = [
  buttonClass
] %}

{% set type = content.field_button_icon_type['#items'].value %}
{% if type == 'goto' %}
  {% set icon = 'caret-right' %}
{% elseif type == 'download' %}
  {% set icon = 'download' %}
{% elseif type == 'watch' %}
  {% set icon = 'play-circle' %}
{% elseif type == 'basket' %}
  {% set icon = 'external-link' %}
{% endif %}

{% set url = content.field_title_and_link.0['#url']  %}
{% set text = content.field_title_and_link.0['#title'] %}
{% if text is empty %}
  {% set text = url %}
{% endif %}

{% block paragraph %}
  {% if url %}
    <div{{ attributes.addClass(classes) }}>
      {% block content %}

        <a href="{{ url }}"
          {% if field_open_new_tab == 1 %}
            target="_blank" rel="noopener noreferrer"
          {% endif %}>

          {{ text }}

          {% if icon %}
            <svg class="vsc-icon">
              <use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--{{ icon }}"></use>
            </svg>
          {% endif %}
        </a>

      {% endblock %}
    </div>
  {% endif %}
{% endblock paragraph %}
