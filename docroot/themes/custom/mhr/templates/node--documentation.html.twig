
{%
  set classes = [
  'vits-article',
  'mhr-documentation-page',
  not node.isPublished() ? 'node--unpublished',
  mhr_documentation.page_title_display ? 'page-title-display-'~mhr_documentation.page_title_display|clean_class
]
%}

<article{{ attributes.addClass(classes) }}>

  {# Hero area #}
  {% if (mhr_documentation.image_no_lazy is not empty) %}
    <div class="mhr-documentation_hero">
      <div class="width-container--xx-narrow gutter--responsive-padding section-padding--small">
        <div class="vits-article__featured_image">{{ mhr_documentation.image_no_lazy }}</div>
      </div>
    </div>
  {% endif %}

  {# Top content area #}
  {% if (mhr_documentation.top_page_content is not empty) %}
    <div class="top-page-content-layout">
      <div class="">
        <div class="top-page-content">{{ mhr_documentation.top_page_content }}</div>
      </div>
    </div>
  {% endif %}


  {# Main content area #}
  <div class="vsc-component-layout width-container gutter--responsive-padding section-padding--small-top">

    <div class="vsc-component-layout__sidebar-1 vsc-component-layout__sticky-element-desktop">
      {# In-page navigation menu #}
      <div class="vits-sectional-nav">
        {{ drupal_menu(mhr_domain_suffix == 'us' ? 'documentation-us' : 'documentation-uk', 2, 3, false) }}
      </div>
    </div>

    <div class="vsc-component-layout__main-content">
      {# Article Main Content Area #}
      {% if mhr_documentation.title %}
        <div class="vits-article__header gutter--responsive-padding main-content-text">
          <h1 class="vits-article__title">{{ mhr_documentation.title }}</h1>
        </div>
      {% endif %}

      <div class="mhr-documentation-page-content">
        {{ mhr_documentation.structured_content_rows }}
      </div>

      <div class="vits-article__footer vits-article__footer--divider gutter--responsive-padding">
        {{ drupal_block(mhr_domain_suffix == 'us' ? 'menu_pager_block:documentation-us' : 'menu_pager_block:documentation-uk',{menu_pager_restrict_to_parent: true, menu_pager_custom_label: true, menu_pager_hide_menu_title: false, menu_pager_previous_label: '', menu_pager_next_label: ''}) }}
      </div>
    </div>
  </div>

</article>

