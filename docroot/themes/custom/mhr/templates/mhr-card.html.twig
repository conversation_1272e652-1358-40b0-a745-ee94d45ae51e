{#
  A generic 'card' template to be used in various places.
#}

{% if card.card_custom_wrapper_classes_array is not defined %}
  {% set card_custom_wrapper_classes_array = [] %}
{% else %}
  {% set card_custom_wrapper_classes_array = card.card_custom_wrapper_classes_array %}
{% endif %}

{%
  set wrapper_classes = [
    'mhr-card',
  ]|merge( card_custom_wrapper_classes_array )
%}

{%
  set wrapper_classes_string = wrapper_classes|join(' ')
%}

{#
  card.card_link could contain multiple values. If it contains just one, the whole
  card is made to link to that destination. If it contains multiple values, these
  are output in the card with the standard button style.
#}
{% if card.card_link is not empty %}
  {% if card.card_link.url is defined %}
    {% set single_card_link = card.card_link %}
  {% else %}
    {% set multiple_card_links = card.card_link %}
  {% endif %}
{% endif %}
<div class="{{ wrapper_classes_string }}{% if single_card_link.url is not empty %} mhr-card--has-link{% endif %}">
  {% if single_card_link.url is not empty %}
    <a class="mhr-card__content" href="{{ single_card_link.url }}" title="{{ single_card_link.label }}">
  {% else %}
    <div class="mhr-card__content">
  {% endif %}
    {% if card.card_image %}
      <div class="mhr-card__image">
        {% if card.card_tag %}
          <div class="mhr-card__tag tag-lozenge-card-overlay">{{ card.card_tag }}</div>
        {% endif %}

        {{ card.card_image }}
      </div>
    {% endif %}
    {% if card.card_date or card.card_heading or card.card_body %}
      <div class="mhr-card__text">
        {% if card.card_date %}
          <p class="mhr-card__date">{{ card.card_date }}</p>
        {% endif %}
        {% if card.card_heading %}
          <h3 class="text--h5 mhr-card__heading">{{ card.card_heading }}</h3>
        {% endif %}
        {% if card.card_body %}
          <div class="mhr-card__body main-content-text">{{ card.card_body }}</div>
        {% endif %}
      </div>
    {% endif %}
    {% if multiple_card_links is not empty %}
      <div class="mhr-card__ctas-container">
        {% for one_of_multiple_card_links in multiple_card_links %}
          <div class="vsc-button-item-outline">
            <a href="{{ one_of_multiple_card_links.url }}" title="{{ one_of_multiple_card_links.label }}">{{ one_of_multiple_card_links.label }}</a>
          </div>
        {% endfor %}
      </div>
    {% endif %}
    {% if single_card_link.url is not empty %}
      <div class="mhr-card__trigger">
        <svg class="vsc-icon">
          <use xlink:href="{{ drupal_url(active_theme_path()) }}/dist/icons/icons.svg#icon--arrow-right"></use>
        </svg>
      </div>
    {% endif %}
  {% if single_card_link.url is not empty %}
    </a>
  {% else %}
    </div>
  {% endif %}

</div>
