{#
/**
 * @file
 * Theme implementation to display a grid paragraph.
 *
 * Overrides /templates/paragraph.html.twig in the paragraphs module directory.
 * See that file for details of available variables and associated preprocess
 * function.
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'paragraph',
    'paragraph--type--' ~ paragraph.bundle|clean_class,
    view_mode ? 'paragraph--view-mode--' ~ view_mode|clean_class,
    not paragraph.isPublished() ? 'paragraph--unpublished',
  ]
%}

{% block paragraph %}
  <div{{ attributes.addClass(classes) }}>
    {% block content %}
      <div class="paragraph paragraph--type--mhr-mini-slider-stripe paragraph--view-mode--default animate grid-row-of-{{ content.field_image_link_items_per_slide.0 }}">
        <div class="field field--name-field-stripe-items field--type-entity-reference-revisions field--label-visually_hidden mini-slider">
          <button class="mini-slider__arrow mini-slider__arrow--prev">Prev <span class="icn-chevron-left"></span></button>
          <div class="mini-slider__outer">
            <div class="mini-slider__inner">
              {% for item in paragraph.field_image_link_carousel_items %}
                <div class="mini-slider__item">{{ item | view }}</div>
              {% endfor %}
            </div>
          </div>
          <button class="mini-slider__arrow mini-slider__arrow--next">Next <span class="icn-chevron-right"></span></button>
        </div>
      </div>
    {% endblock %}
  </div>
{% endblock paragraph %}
