{%
  set classes = [
    'vits-component',
    'vits-icon-buttons'
  ]
%}
{% block paragraph %}
	<div{{attributes.addClass(classes)}}>
		<div class="width-container gutter--responsive-padding section-padding vsc-dark-background">

      {% block content %}

        {# Header #}
				{% if icon_buttons_output.icon_buttons_output_title is not empty or icon_buttons_output.icon_buttons_output_subheading is not empty %}
					<div class="vits-icon-buttons__header width-container--xxx-narrow">
						{% if icon_buttons_output.icon_buttons_output_title is not empty %}
							<h2 class="vits-icon-buttons__heading text--heading-large">{{ icon_buttons_output.icon_buttons_output_title }}</h2>
						{% endif %}
						{% if icon_buttons_output.icon_buttons_output_subheading is not empty %}
							<p class="vits-icon-buttons__subheading text--p-large">{{ icon_buttons_output.icon_buttons_output_subheading }}</p>
						{% endif %}
					</div>
				{% endif %}

        {# Icon Buttons #}
				{% if icon_buttons_output.icon_buttons_output_buttons is not empty %}
					<div class="vits-icon-buttons_grid grid-container columns--3">
						{% for icon_button in icon_buttons_output.icon_buttons_output_buttons %}
							<div class="vits-icon-button">
								<div class="vits-icon-button__image">
									{% if icon_button.icon_image is not empty %}
										{{ icon_button.icon_image }}
									{% else %}
										<div class="vits-icon-button__image-inner">
											{% if icon_button.icon_type == 'Scheduleacall' %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--device-mobile"></use>
												</svg>
											{% elseif icon_button.icon_type == 'Downloadbrochure' %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--download"></use>
												</svg>
											{% elseif icon_button.icon_type == 'Bookyourdemo' %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--laptop"></use>
												</svg>
											{% elseif icon_button.icon_type == 'Buynow' %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--shopping-cart"></use>
												</svg>
											{% elseif icon_button.icon_type == 'Askusaquestion' %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--chats"></use>
												</svg>
											{% else %}
												<svg class="vsc-icon">
													<use xlink:href="/themes/custom/mhr/dist/icons/icons.svg#icon--chats"></use>
												</svg>
											{% endif %}
										</div>
									{% endif %}
								</div>
								{% if icon_button.icon_title is not empty %}
									<h3 class="vits-icon-button__heading text--h4">{{ icon_button.icon_title }}</h3>
								{% endif %}
								{% if icon_button.icon_link_url is not empty %}
									<div class="vsc-button-item-outline-light vsc-button-item-text-large">
										<a href="{{ icon_button.icon_link_url }}">{{ icon_button.icon_link_title }}</a>
									</div>
								{% endif %}
							</div>
						{% endfor %}
					</div>
				{% endif %}

			{% endblock %}

		</div>
	</div>
{% endblock paragraph %}
