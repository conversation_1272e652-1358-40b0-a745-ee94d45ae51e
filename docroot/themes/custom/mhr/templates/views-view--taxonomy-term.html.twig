{#
/**
 * @file
 * Theme override for a main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 */
#}
{%
  set classes = [
    'view',
    'view-' ~ id|clean_class,
    'view-id-' ~ id,
    'view-display-id-' ~ display_id,
    dom_id ? 'js-view-dom-id-' ~ dom_id,
    'vsc-view-card-grid',
    'vits-taxonomy-term'
  ]
%}

<article{{ attributes.addClass(classes) }}>
    
  {% if header %}
    <div class="vits-component vits-title-hero-banner">
      <div class="width-container--xx-narrow gutter--responsive-padding section-padding">
        {{ header }}
      </div>
    </div>
  {% endif %}

  <div class="width-container gutter--responsive-padding section-padding--bottom section-padding--top">
    
      {% if rows %}
        <div class="vsc-view-card-grid__content view-content">
          {{ rows }}
        </div>
      {% elseif empty %}
        <div class="view-empty text--align-center width-container--xxx-narrow section-padding--top">
          {{ empty }}
        </div>
      {% endif %}

      {# Pager #}
      {% if pager %}
        {{ pager }}
      {% endif %}
    </div>

  </div>

</article>
