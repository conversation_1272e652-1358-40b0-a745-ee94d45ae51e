{#
/**
 * @file
 * Theme implementation to display an product_feature paragraph.
 *
 * Overrides /templates/paragraph.html.twig in the paragraphs module directory.
 * See that file for details of available variables and associated preprocess
 * function.
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'paragraph',
    'paragraph--type--' ~ paragraph.bundle|clean_class,
    view_mode ? 'paragraph--view-mode--' ~ view_mode|clean_class,
    not paragraph.isPublished() ? 'paragraph--unpublished'
  ]
%}
{% block paragraph %}
    {% block content %}
      {{ content }}
    {% endblock %}
{% endblock paragraph %}
