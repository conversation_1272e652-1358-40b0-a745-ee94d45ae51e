{% set mhr_teaser_card = {
  'card_body': mhr_news_article.body,
  'card_custom_wrapper_classes_array': [],
  'card_date': mhr_news_article.created_date_for_teaser,
  'card_heading': mhr_news_article.title,
  'card_image': mhr_news_article.image,
  'card_link': {
    'url': mhr_news_article.link_to_post.url,
    'label': mhr_news_article.link_to_post.label
  },
}
%}
{# Include generic card template. Path includes /../mhr/ so it finds the template in the MHR #}
{# theme, even when in the mhr_admin theme.                                                  #}
{{ include(active_theme_path()~'/../mhr/templates/mhr-card.html.twig', { card: mhr_teaser_card }) }}