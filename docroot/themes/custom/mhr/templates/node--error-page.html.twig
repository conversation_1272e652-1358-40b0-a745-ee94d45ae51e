{#
/**
 * @file
 * Theme override to display an Error page node.
 *
 * Overrides /core/themes/classy/templates/content/node.html.twig
 * See that file for details of available variables and associated preprocess
 * function.
 */
#}
{%
  set classes = [
    'vits-error-page',
    not node.isPublished() ? 'node--unpublished'
  ]
%}

<article{{ attributes.addClass(classes) }}>
  <div class="width-container--xxx-narrow gutter--responsive-padding section-padding--small-top section-padding--bottom text--align-center">

    {# Error Main Content #}
    <div class="vits-error-page__main-content">
      <div class="vits-error-page__text-content">
        <h1 class="text--heading-large">{{ mhr_error_page.title }}</h1>
        <h2 class="text--h5">{{ mhr_error_page.explanation }}</h2>
      </div>

      <svg class="vits-error-page__border-icon" viewBox="0 0 145.1 145.1" preserveAspectRatio="none">
        <path fill="#CA1431" d="M8.8,8.8h2.8v22.7H8.8V8.8z M8.8,8.8h22.7v2.8H8.8V8.8z"/>
        <path fill="#CA1431" d="M8.8,133.5h22.7v2.8H8.8V133.5z M8.8,113.7h2.8v22.7H8.8V113.7z"/>
        <path fill="#CA1431" d="M133.5,113.7h2.8v22.7h-2.8V113.7z M113.7,133.5h22.7v2.8h-22.7V133.5z"/>
        <path fill="#CA1431" d="M113.7,8.8h22.7v2.8h-22.7V8.8z M133.5,8.8h2.8v22.7h-2.8V8.8z"/>
      </svg>
    </div>



    {# Suggested Links #}
    {% if mhr_error_page.suggested_links %}
      <div class="vits-error-page__suggested-links">
        <h3 class="text--h6">Here are a few links that could be helpful</h3>

        <div class="vits-error-page__suggested-link-items main-content-text">
          {% for link in mhr_error_page.suggested_links %}
            <a href="{{ link.link }}">{{ link.title }}</a>
          {% endfor %}
        </div>

      </div>
    {% endif %}



    {# CTA Button #}
    {% if mhr_error_page.error_button %}
      {% for error_button_single in mhr_error_page.error_button %}
        <p><div class="vsc-button-item-outline vsc-button-item-narrow"><a href="{{ error_button_single.link }}" title="{{ error_button_single.title }}">{{ error_button_single.title }}</a></div></p>
      {% endfor %}
    {% endif %}

  </div>
</article>
