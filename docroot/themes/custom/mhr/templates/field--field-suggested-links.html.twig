{#
/**
* @file
* Theme override for the Suggested links field.
*
* Overrides /core/themes/classy/templates/field/field.html.twig
* See that file for details of available variables and associated preprocess
* function.
*/
#}

{%
  set classes = [
    'field',
    'field--name-' ~ field_name|clean_class,
    'field--type-' ~ field_type|clean_class,
    'field--label-' ~ label_display,
  ]
%}
{%
  set title_classes = [
    'field__label',
    label_display == 'visually_hidden' ? 'visually-hidden',
  ]
%}
    {% for item in items %}
        <a href="{{ item.content['#url'] }}">{{ item.content['#title'] }}</a>
    {% endfor %}

