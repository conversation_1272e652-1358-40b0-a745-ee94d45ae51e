{#
/**
 * @file
 * Overrides /core/themes/classy/templates/content/node.html.twig
 * See that file for details of available variables and associated preprocess
 * function.
 */
#}
<div class="featured-news--item">
  {% if content.field_background_image|render|striptags|trim is not empty %}
    <div class="featured-news--item--image">
      {{ content.field_background_image }}
    </div>
  {% endif %}
  <div class="featured-news--item--text">
    <h6>Featured Event</h6>
    <h4><a href="{{ url }}">{{ label }}</a></h4>
    <p class="date">{{ event_date_range }}</p>
    {% if event_description|render|striptags|trim is not empty %}
      <p>{{ event_description }}</p>
    {% endif %}
    <span class="field--name-field-button">
      <a href="{{ url }}">Read more</a>
    </span>
  </div>
</div>
