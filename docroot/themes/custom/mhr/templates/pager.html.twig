{#
/**
 * @file
 * Theme override to display a pager.
 *
 * Overrides /core/themes/classy/templates/navigation/pager.html.twig
 * See that file for documentation about variables available etc in this template.
 */
#}
{% if items %}
  <nav class="vsc-pager" role="navigation" aria-labelledby="{{ heading_id }}">
    <h4 id="{{ heading_id }}" class="visually-hidden">{{ 'Pagination'|t }}</h4>

    <div class="vsc-pager__main-content">
      {# Previous Link #}
      <div class="vsc-pager__previous">
        {# Print previous item if we are not on the first page. #}
        {% if items.previous %}
          <a class="vsc-pager__previous-link" href="{{ items.previous.href }}" rel="prev"{{ items.previous.attributes|without('href', 'title', 'rel') }} aria-label="Go to previous page">
            <span class="visually-hidden">{{ 'Previous page'|t }}</span>
            <svg class="vsc-icon vsc-icon--small">
              <use xlink:href="{{ drupal_url(active_theme_path()) }}/dist/icons/icons.svg#icon--caret-right"></use>
            </svg>
          </a>
        {% endif %}
      </div>


      {# Pager Items #}
      <ul class="vsc-pager__items">

        {# Add an ellipsis if there are further previous pages. #}
        {% if ellipses.previous %}
          <li class="vsc-pager__item vsc-pager__item--ellipsis" role="presentation">&hellip;</li>
        {% endif %}

        {# Now generate the actual pager piece. #}
        {% for key, item in items.pages %}
          <li class="vsc-pager__item{{ current == key ? ' is-active' : '' }}">
            {% if current == key %}
              {% set title = 'Current page'|t %}
            {% else %}
              {% set title = 'Go to page @key'|t({'@key': key}) %}
            {% endif %}
            <a href="{{ item.href }}" title="{{ title }}"{{ item.attributes|without('href', 'title') }}>
              <span class="visually-hidden">
                {{ current == key ? 'Current page'|t : 'Page'|t }}
              </span>
              {{- key -}}
            </a>
          </li>
        {% endfor %}

        {# Add an ellipsis if there are further next pages. #}
        {% if ellipses.next %}
          <li class="vsc-pager__item vsc-pager__item--ellipsis" role="presentation">&hellip;</li>
        {% endif %}
      </ul>


      {# Next Link #}
      <div class="vsc-pager__next">
        {# Print next item if we are not on the last page. #}
        {% if items.next %}
          <a class="vsc-pager__next-link" href="{{ items.next.href }}" rel="next"{{ items.next.attributes|without('href', 'title', 'rel') }} aria-label="Go to next page">
            <span class="visually-hidden">{{ 'Next page'|t }}</span>
            <svg class="vsc-icon vsc-icon--small">
              <use xlink:href="{{ drupal_url(active_theme_path()) }}/dist/icons/icons.svg#icon--caret-right"></use>
            </svg>
          </a>
        {% endif %}
      </div>
    </div>
  </nav>
{% endif %}
