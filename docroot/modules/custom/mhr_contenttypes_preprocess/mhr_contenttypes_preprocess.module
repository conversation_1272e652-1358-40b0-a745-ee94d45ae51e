<?php
/**
 * @file
 * Preprocessing hooks for Content Types used in MHR website.
 */

use <PERSON><PERSON><PERSON>\file\Entity\File;
use <PERSON><PERSON>al\Core\Datetime\DrupalDateTime;
use Drupal\Core\Url;
use Drupal\mhr_contenttypes_preprocess\MHRContentTypeUtilities;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Dr<PERSON>al\taxonomy\Entity\Term;

/*
 * Implements hook_preprocess_node()
 *
 */
function mhr_contenttypes_preprocess_preprocess_node(&$variables) {

  $variables['mhr_domain_suffix'] = _mhr_get_current_domain_suffix();

  /**
   * @var MHRContentTypeUtilities
   */
  $mhr_content_type_utilities = \Drupal::service('mhr_content_type_utilities');
  // Estimated reading time, which may exist on several content types.
  $variables['mhr_estimated_reading_time'] = _mhr_get_estimated_reading_time($variables['node']);

  // Get the node object.
  /** @var Node $node */
  $node = $variables['node'];

  // Set up 'public' labelling of content types.
  $variables['mhr_public_content_type_tag'] = $mhr_content_type_utilities->getMHRPublicContentTypeLabel($node);

  // Preprocessing and variables setup for MHR Sidebar CTA, which may be added
  // to various content types.
  if (
    $node->hasField('field_mhr_sidebar_cta') &&
    $node->field_mhr_sidebar_cta->isEmpty() === FALSE
  ) {
    // Process mhr_sidebar_cta Paragraphs referenced by field_mhr_sidebar_cta.
    foreach($node->field_mhr_sidebar_cta->referencedEntities() as $single_mhr_sidebar_cta) {
      // Background colour (field_mhr_sidebar_cta_bg_colour). A button style is chosen here
      // as well, based on the background colour.
      $single_mhr_sidebar_cta_background_colour_class = 'mhr-sidebar-cta__bg-colour-mhr-blue vsc-dark-background'; // Default.
      $single_mhr_sidebar_cta_button_class = 'vsc-button-item-outline-light'; // Default.
      if (
        $single_mhr_sidebar_cta->hasField('field_mhr_sidebar_cta_bg_colour') &&
        !$single_mhr_sidebar_cta->get('field_mhr_sidebar_cta_bg_colour')->isEmpty()
      ) {
        $single_mhr_sidebar_cta_field_mhr_sidebar_cta_bg_colour = $single_mhr_sidebar_cta->get('field_mhr_sidebar_cta_bg_colour')->value;
        // Set up class(es) to be added to the component, based on the selection made
        // for its instance.
        switch ($single_mhr_sidebar_cta_field_mhr_sidebar_cta_bg_colour) {
          case 'mhr_sidebar_cta_bg_colour_light_grey':
            $single_mhr_sidebar_cta_background_colour_class = 'mhr-sidebar-cta__bg-colour-light-grey';
            $single_mhr_sidebar_cta_button_class = 'vsc-button-item-solid';
            break;
          case 'mhr_sidebar_cta_bg_colour_mhr_blue':
          default:
            $single_mhr_sidebar_cta_background_colour_class = 'mhr-sidebar-cta__bg-colour-mhr-blue vsc-dark-background';
            $single_mhr_sidebar_cta_button_class = 'vsc-button-item-outline-light';
            break;
          case 'mhr_sidebar_cta_bg_colour_white':
            $single_mhr_sidebar_cta_background_colour_class = 'mhr-sidebar-cta__bg-colour-white';
            $single_mhr_sidebar_cta_button_class = 'vsc-button-item-solid';
            break;
        }
      }
      // Image (field_mhr_sidebar_cta_image).
      $single_mhr_sidebar_cta_image_render_array = null;
      if (
        $single_mhr_sidebar_cta->hasField('field_mhr_sidebar_cta_image') &&
        $single_mhr_sidebar_cta->field_mhr_sidebar_cta_image->isEmpty() === FALSE
      ) {
        $single_mhr_sidebar_cta_image_data = _mhr_get_image_data($single_mhr_sidebar_cta->field_mhr_sidebar_cta_image);
        if ($single_mhr_sidebar_cta_image_data) {
          $single_mhr_sidebar_cta_image_render_array = [
            '#height' => $single_mhr_sidebar_cta_image_data['image_height'],
            '#responsive_image_style_id' => 'mhr_sidebar_cta_image',
            '#theme' => 'responsive_image',
            '#uri' => $single_mhr_sidebar_cta_image_data['image_uri'],
            '#width' => $single_mhr_sidebar_cta_image_data['image_width'],
          ];
        }
      }
      // Text (field_mhr_sidebar_cta_text).
      $field_mhr_sidebar_cta_text_data = null;
      if (
        $single_mhr_sidebar_cta->hasField('field_mhr_sidebar_cta_text') &&
        $single_mhr_sidebar_cta->field_mhr_sidebar_cta_text->isEmpty() === FALSE
      ) {
        $field_mhr_sidebar_cta_text_data = [
          '#type' => 'inline_template',
          '#template' => $single_mhr_sidebar_cta->field_mhr_sidebar_cta_text->value,
        ];
      }
      // Link (field_mhr_sidebar_cta_link).
      $single_mhr_sidebar_cta_link_label = null;
      $single_mhr_sidebar_cta_link_url = null;
      if (
        $single_mhr_sidebar_cta->hasField('field_mhr_sidebar_cta_link') &&
        $single_mhr_sidebar_cta->field_mhr_sidebar_cta_link->isEmpty() === FALSE
      ) {
        $single_mhr_sidebar_cta_link_data = $single_mhr_sidebar_cta->field_mhr_sidebar_cta_link->getValue()[0];
        $single_mhr_sidebar_cta_link_label = isset($single_mhr_sidebar_cta_link_data['title']) ? $single_mhr_sidebar_cta_link_data['title'] : null;
        $single_mhr_sidebar_cta_link_url = isset($single_mhr_sidebar_cta_link_data['uri']) ? Url::fromUri($single_mhr_sidebar_cta_link_data['uri']) : null;
      }
      // Gather data together to pass to template.
      $field_mhr_sidebar_cta_data[] = [
        'mhr_sidebar_cta_background_colour_class' => $single_mhr_sidebar_cta_background_colour_class,
        'mhr_sidebar_cta_button_class' => $single_mhr_sidebar_cta_button_class,
        'mhr_sidebar_cta_image_render_array' => $single_mhr_sidebar_cta_image_render_array,
        'mhr_sidebar_cta_link_label' => $single_mhr_sidebar_cta_link_label,
        'mhr_sidebar_cta_link_url' => $single_mhr_sidebar_cta_link_url,
        'mhr_sidebar_cta_text' => $field_mhr_sidebar_cta_text_data,
      ];
    }
    $variables['mhr_sidebar_cta'] = $field_mhr_sidebar_cta_data;
  }

  // Preprocess nodes to set up variables for templates.
  // Doing this within the generic mhr_contenttypes_preprocess_preprocess_node
  // as for some reason it isn't picked up when attempting to use
  // mhr_contenttypes_preprocess_preprocess_node__{content_type}.

  switch($node->getType()) {

    case 'abm_page':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_abm_page'] = $mhr_node_variables;
      break;

    case 'article':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // created_date
      $mhr_node_variables['created_date'] = date('j F Y', $node->created->value);

      // created_date_for_teaser
      $mhr_node_variables['created_date_for_teaser'] = _mhr_fuzzy_date_for_teaser($node->created->value);

      // article title
      $mhr_node_variables['title'] = $node->getTitle();

      // image
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $responsive_image_style = 'content_hub_card_teaser';
          break;
        case 'featured':
          $responsive_image_style = 'featured_blog';
          break;
        case 'default':
        case 'full':
        default:
          $responsive_image_style = 'product_image';
          break;
      }

      $mhr_node_variables['image'] = null;
      $mhr_node_variables['image_no_lazy'] = null;

      if ($node->hasField('field_image') && !$node->field_image->isEmpty()) {
        $article_image_data = _mhr_get_image_data($node->field_image);

        if ($article_image_data) {

          // Base image build array
          $image_build_base = [
            '#theme' => 'responsive_image',
            '#uri' => $article_image_data['image_uri'],
            '#responsive_image_style_id' => $responsive_image_style,
            '#width' => $article_image_data['image_width'],
            '#height' => $article_image_data['image_height'],
            '#attributes' => [
              'alt' => $node->get('field_image')->entity->get('field_media_image')->alt,
            ],
          ];

          // Image with lazy loading
          $image_build_lazy = $image_build_base;
          $image_build_lazy['#attributes']['loading'] = 'lazy';
          $mhr_node_variables['image'] = $image_build_lazy;

          // Image without lazy loading (for 'full' view mode)
          $image_build_no_lazy = $image_build_base;
          $image_build_no_lazy['#attributes']['loading'] = 'eager';
          $mhr_node_variables['image_no_lazy'] = $image_build_no_lazy;
        }
      }

      // intro
      switch ($view_mode) {
        case 'featured':
        case 'full':
          $mhr_node_variables['intro'] = $node->get('field_intro')->value;
          break;
      }

      // body
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['body'] = $node->body->view([
            'label' => 'hidden',
            'type' => 'smart_trim',
            'settings' => [
              'summary_handler' => 'trim',
              'trim_length' => 30,
              'trim_suffix' => '…',
              'trim_type' => 'words',
              'trim_options' => [
                'text' => true,
              ],
            ],
          ]);
          break;
        default:
          $mhr_node_variables['body'] = check_markup($node->get('body')->value, $node->get('body')->format);
          break;
      }

      // button
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // link to post
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'featured':
        case 'teaser':
          $mhr_node_variables['link_to_post'] = [
            'label' => $mhr_node_variables['title'],
            'url' => Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => TRUE]),
          ];
          break;
      }

      // tags - multiple
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['tags'] = null;
          if (!$node->get('field_tags')->isEmpty()) {
            $tags = [];
            foreach ($node->get('field_tags') as $tag) {
              $tag_link = Url::fromRoute('entity.taxonomy_term.canonical', ['taxonomy_term' => $tag->entity->id()])
                ->toString();
              $tags[] = [
                'title' => $tag->entity->getName(),
                'link' => $tag_link
              ];
            }
            $mhr_node_variables['tags'] = $tags;
          }
          break;
      }

      // Add node data to variables
      $variables['mhr_news_article'] = $mhr_node_variables;

      // Set up related news articles
      $variables['mhr_show_related_news_articles'] = views_embed_view('mhr_related_news_articles', 'default', $variables['node']->id(), $variables['node']->get('field_tags')->target_id);
      break;

    case 'basic_page':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // body
      $body_content = null;
      if ($node->body->isEmpty() === FALSE ) {
        $body_content = check_markup($node->get('body')->value, $node->get('body')->format);
      }
      $mhr_node_variables['body'] = $body_content;

      // Add node data to variables
      $variables['mhr_basic_page'] = $mhr_node_variables;
      break;

    case 'blog_post':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for node data
      $mhr_node_variables = [];

      // created_date
      $mhr_node_variables['created_date'] = date('j F Y', $node->created->value);

      // created_date_for_teaser
      $mhr_node_variables['created_date_for_teaser'] = _mhr_fuzzy_date_for_teaser($node->created->value);

      // blog title
      $mhr_node_variables['title'] = $node->getTitle();

      // Image
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $responsive_image_style = 'content_hub_card_teaser';
          break;
        case 'featured':
          $responsive_image_style = 'featured_blog';
          break;
        case 'default':
        case 'full':
        default:
          $responsive_image_style = 'product_image';
          break;
      }

      $mhr_node_variables['image'] = null;
      $mhr_node_variables['image_no_lazy'] = null;

      $blog_post_image_data = _mhr_get_image_data($node->field_image);

      if ($blog_post_image_data) {
        // Base image build array
        $image_build_base = [
          '#theme' => 'responsive_image',
          '#uri' => $blog_post_image_data['image_uri'],
          '#responsive_image_style_id' => $responsive_image_style,
          '#width' => $blog_post_image_data['image_width'],
          '#height' => $blog_post_image_data['image_height'],
          '#attributes' => [
            'alt' => $node->get('field_image')->entity->get('field_media_image')->alt,
          ],
        ];

        // Image with lazy loading
        $image_build_lazy = $image_build_base;
        $image_build_lazy['#attributes']['loading'] = 'lazy';
        $mhr_node_variables['image'] = $image_build_lazy;

        // Image without lazy loading (for 'full' view mode)
        $image_build_no_lazy = $image_build_base;
        $image_build_no_lazy['#attributes']['loading'] = 'eager';
        $mhr_node_variables['image_no_lazy'] = $image_build_no_lazy;
      }

      // intro
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'featured':
        case 'full':
        case 'teaser':
          $mhr_node_variables['intro'] = $node->get('field_intro')->value;
          break;
      }

      // body
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['body'] = $node->body->view([
            'label' => 'hidden',
            'type' => 'smart_trim',
            'settings' => [
              'summary_handler' => 'trim',
              'trim_length' => 30,
              'trim_suffix' => '…',
              'trim_type' => 'words',
              'trim_options' => [
                'text' => true,
              ],
            ],
          ]);
          break;
        default:
          $mhr_node_variables['body'] = check_markup($node->get('body')->value, $node->get('body')->format);
          break;
      }

      // link to post
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'featured':
        case 'teaser':
          $mhr_node_variables['link_to_post'] = [
            'label' => $mhr_node_variables['title'],
            'url' => Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => TRUE]),
          ];
          break;
      }

      // button
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // tags - multiple
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['tags'] = null;
          if (!$node->get('field_blog_tags')->isEmpty()) {
            $tags = [];
            foreach ($node->get('field_blog_tags')->referencedEntities() as $tag) {
              $tag_link = Url::fromRoute('entity.taxonomy_term.canonical', ['taxonomy_term' => $tag->id()])
                ->toString();
              $tags[] = [
                'title' => $tag->getName(),
                'link' => $tag_link
              ];
            }
            $mhr_node_variables['tags'] = $tags;
          }
          break;
      }

      // author image
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['author_image'] = null;
          if (!$node->get('field_author')->isEmpty() && !$node->get('field_author')->entity->get('field_author_image')->isEmpty()) {
            $file = File::load($node->get('field_author')->entity->get('field_author_image')->entity->id());
            $uri = $file->getFileUri();
            $image_build = [
              '#theme' => 'image_style',
              '#uri' => $uri,
              '#style_name' => 'blog_author',
              '#alt' => $node->get('field_author')->entity->get('field_author_image')->alt,
            ];
            $mhr_node_variables['author_image'] = \Drupal::service('renderer')
              ->render($image_build);
          }
          break;
      }

      // author name
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['author_name'] = null;
          if (!$node->get('field_author')->isEmpty() && !empty($mhr_node_variables['author_name'] = $node->get('field_author')->entity->getName())) {
            $mhr_node_variables['author_name'] = $node->get('field_author')->entity->getName();
          }
          break;
      }

      // author bio
      switch ($view_mode) {
        case 'full':
          $mhr_node_variables['author_bio'] = null;
          if (!$node->get('field_author')->isEmpty() && !empty($node->get('field_author')->entity->get('field_author_biography')->value)) {
            $mhr_node_variables['author_bio'] = check_markup($node->get('field_author')->entity->get('field_author_biography')->value, $node->get('field_author')->entity->get('field_author_biography')->format);
          }
          break;
      }

      // card tag
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['tag'] = 'Blog';
          break;
      }

      // Set up related blog posts
      $related_blog_post_ids = views_get_view_result('mhr_related_blog_posts', 'default', $variables['node']->id(), $variables['node']->get('field_blog_tags')->target_id);
      $related_blog_post_entities = [];
      foreach($related_blog_post_ids as $related_blog_post_ids_single) {
        $related_blog_post_entities[] = $related_blog_post_ids_single->_entity;
      }
      $variables['mhr_show_related_blog_posts'] = _mhr_render_blog_post_teasers($related_blog_post_entities);

      // Add node data to variables
      $variables['mhr_blog_post'] = $mhr_node_variables;

      break;

    case 'case_study':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for node data
      $mhr_node_variables = [];

      // title
      $field_heading = null;
      if (
        $node->hasField('field_heading') &&
        $node->field_heading->isEmpty() == FALSE
      ) {
        $field_heading = $node->field_heading->value;
      }
      $mhr_node_variables['title'] = $field_heading;

      // image
      $mhr_node_variables['image'] = null;
      if (
        $node->hasField('field_resource_image') &&
        $node->field_resource_image->isEmpty() == FALSE
      ) {
        $case_study_image_data = _mhr_get_image_data($node->field_resource_image);
        if ($case_study_image_data) {
          $mhr_node_variables['image'] = [
            '#height' => $case_study_image_data['image_height'],
            '#responsive_image_style_id' => 'content_hub_card_teaser',
            '#theme' => 'responsive_image',
            '#uri' => $case_study_image_data['image_uri'],
            '#width' => $case_study_image_data['image_width'],
          ];
        }
      }

      // link
      $case_study_link_url = null;
      $case_study_link_label = null;
      if (
        $node->hasField('field_button') &&
        $node->field_button->isEmpty() == FALSE
      ) {
        $field_button = $node->field_button->first();
        if ($field_button) {
          $case_study_link_label = $field_button->title;
          $case_study_link_url = $field_button->getUrl()->toString();
        }
      }
      $mhr_node_variables['link'] = [
        'label' => $case_study_link_label,
        'url' => $case_study_link_url,
      ];

      // body
      $mhr_node_variables['body'] = $node->body->view([
        'label' => 'hidden',
        'type' => 'smart_trim',
        'settings' => [
          'summary_handler' => 'trim',
          'trim_length' => 30,
          'trim_suffix' => '…',
          'trim_type' => 'words',
          'trim_options' => [
            'text' => true,
          ],
        ],
      ]);

      // created_date
      $mhr_node_variables['created_date_for_teaser'] = _mhr_fuzzy_date_for_teaser($node->created->value);

      // Add node data to variables
      $variables['mhr_case_study_listing_item'] = $mhr_node_variables;
      break;

    case 'documentation':
      // Get the node
      $node = $variables['node'];

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Get the page title output style
      $page_title_output = 'output_h1'; // Default to outputting as H1 as that is what it has always done
      if (
        $node->hasField('field_page_title_display') &&
        $node->field_page_title_display->isEmpty() == FALSE
      ) {
        $page_title_output = $node->field_page_title_display->value;
      }
      $mhr_node_variables['page_title_display'] = $page_title_output;


      if ($page_title_output !== 'no_output') {
        $mhr_node_variables['title'] = $node->getTitle();
      }

      // field_top_page_content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_top_page_content')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['top_page_content'] = \Drupal::service('renderer')->render($layout_paragraphs_output);


      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_documentation'] = $mhr_node_variables;
      break;

    case 'download_listing_item':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for node data
      $mhr_node_variables = [];

      // title
      $field_heading = null;
      if (
        $node->hasField('field_heading') &&
        $node->field_heading->isEmpty() == FALSE
      ) {
        $field_heading = $node->field_heading->value;
      }
      $mhr_node_variables['title'] = $field_heading;

      // image
      $mhr_node_variables['image'] = null;
      if (
        $node->hasField('field_resource_image') &&
        $node->field_resource_image->isEmpty() == FALSE
      ) {
        $case_study_image_data = _mhr_get_image_data($node->field_resource_image);
        if ($case_study_image_data) {
          $mhr_node_variables['image'] = [
            '#height' => $case_study_image_data['image_height'],
            '#responsive_image_style_id' => 'content_hub_card_teaser',
            '#theme' => 'responsive_image',
            '#uri' => $case_study_image_data['image_uri'],
            '#width' => $case_study_image_data['image_width'],
          ];
        }
      }

      // link
      $case_study_link_url = null;
      $case_study_link_label = null;
      if (
        $node->hasField('field_button') &&
        $node->field_button->isEmpty() == FALSE
      ) {
        $field_button = $node->field_button->first();
        if ($field_button) {
          $case_study_link_label = $field_button->title;
          $case_study_link_url = $field_button->getUrl()->toString();
        }
      }
      $mhr_node_variables['link'] = [
        'label' => $case_study_link_label,
        'url' => $case_study_link_url,
      ];

      // body
      $mhr_node_variables['body'] = $node->body->view([
        'label' => 'hidden',
        'type' => 'smart_trim',
        'settings' => [
          'summary_handler' => 'trim',
          'trim_length' => 30,
          'trim_suffix' => '…',
          'trim_type' => 'words',
          'trim_options' => [
            'text' => true,
          ],
        ],
      ]);

      // field_published_date
      $field_published_date = null;
      if (
        $node->hasField('field_published_date') &&
        $node->field_published_date->isEmpty() == FALSE
      ) {
        $field_published_date_object = new DrupalDateTime($node->field_published_date->value);
        $field_published_date = _mhr_fuzzy_date_for_teaser($field_published_date_object->getTimestamp());
      }
      $mhr_node_variables['published_date_for_teaser'] = $field_published_date;

      // Add node data to variables
      $variables['mhr_download_listing_item'] = $mhr_node_variables;
      break;

    case 'error_page':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Title
      $mhr_node_variables['title'] = $node->getTitle();

      // field_text_explanation
      $mhr_node_variables['explanation'] = $node->get('field_text_explanation')->value;

      // field_suggested_links (multiple)
      $mhr_node_variables['suggested_links'] = [];
      if (!$node->get('field_suggested_links')->isEmpty()) {
        foreach ($node->get('field_suggested_links') as $link) {
          $mhr_node_variables['suggested_links'][] = [
            'title' => $link->title,
            'link' => $link->getUrl()->toString()
          ];
        }
      }

      // field_error_button
      if (
        $node->hasField('field_error_button') &&
        $node->field_error_button->isEmpty() == FALSE
      ) {
        $field_error_button_paragraphs = $node->get('field_error_button')->referencedEntities();
        $field_error_button_build = [];
        foreach ($field_error_button_paragraphs as $field_error_button_paragraph) {
          // Pull out specific parts of the Paragraph in order to render them consistently.
          // There are other options available for 'Icon Button' Paragraphs but they're being
          // ignored in this new design for the time being.
          if (
            $field_error_button_paragraph->hasField('field_title_and_link') &&
            $field_error_button_paragraph->field_title_and_link->isEmpty() == FALSE
          ) {
            $field_error_button_build[] = [
              'title' => $field_error_button_paragraph->field_title_and_link->first()->title,
              'link' => $field_error_button_paragraph->field_title_and_link->first()->getUrl()->toString(),
            ];
          }
        }
        $mhr_node_variables['error_button'] = $field_error_button_build;
      }

      // Add node data to variables
      $variables['mhr_error_page'] = $mhr_node_variables;
      break;

    case 'event':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // Background image
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['background_image'] = null;
          if (!empty($node->get('field_background_image')->entity->get('field_media_image')->entity->id())) {
            $file = File::load($node->get('field_background_image')->entity->get('field_media_image')->entity->id());
            $uri = $file->getFileUri();
            $background_image_display_mode = 'hero_banner';
            if (
              $node->hasField('field_hero_banner_depth') &&
              $node->field_hero_banner_depth->isEmpty() == FALSE &&
              $node->field_hero_banner_depth->value == 'shallow'
            ) {
              $background_image_display_mode = 'hero_banner_shallow';
            }

            $image_build = [
              '#theme' => 'responsive_image',
              '#uri' => $uri,
              '#responsive_image_style_id' => $background_image_display_mode,
              '#attributes' => [
                'alt' => $node->get('field_background_image')->entity->get('field_media_image')->alt,
              ]
            ];
            $mhr_node_variables['background_image'] = \Drupal::service('renderer')
              ->render($image_build);
          }
          break;
      }

      // event_date_range - set up in mhr.theme

      // event_address - set up in mhr.theme

      // event_type - set up in mhr.theme

      // button
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // Event description
      $mhr_node_variables['description'] = null;
      switch($view_mode) {
        case 'full':
          if (!$node->get('field_event_description')->isEmpty()) {
            $mhr_node_variables['description'] = check_markup($node->get('field_event_description')->value, $node->get('field_event_description')->format);
          }
          break;
        case 'teaser':
        case 'content_hub_teaser':
          if (
            $node->hasField('field_event_description') &&
            $node->field_event_description->isEmpty() == FALSE
          ) {
            $mhr_node_variables['description'] = $node->field_event_description->view([
              'label' => 'hidden',
              'type' => 'smart_trim',
              'settings' => [
                'summary_handler' => 'trim',
                'trim_length' => 30,
                'trim_suffix' => '…',
                'trim_type' => 'words',
                'trim_options' => [
                  'text' => true,
                ],
              ],
            ]);
          }
          break;
      }

      // Main features
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['main_features'] = null;
          if (!$node->get('field_main_features')->isEmpty()) {
            $mhr_node_variables['main_features'] = check_markup($node->get('field_main_features')->value, $node->get('field_main_features')->format);
          }
          break;
      }

      // Location
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['location'] = null;
          if (!$node->get('field_location')->isEmpty()) {
            $mhr_node_variables['location'] = check_markup($node->get('field_location')->value, $node->get('field_location')->format);
          }
          break;
      }

      // Row content
      switch($view_mode) {
        case 'full':
          // Get the paragraphs from the field and render as layout paragraphs
          $layout_paragraphs_output = $node->get('field_event_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
          $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);
          break;
      }

      // Venue description
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['venue_description'] = null;
          if (!$node->get('field_venue_description')->isEmpty()) {
            $mhr_node_variables['venue_description'] = check_markup($node->get('field_venue_description')->value, $node->get('field_venue_description')->format);
          }
          break;
      }

      // More information
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['more_information'] = null;
          if (!$node->get('field_more_information')->isEmpty()) {
            $mhr_node_variables['more_information'] = check_markup($node->get('field_more_information')->value, $node->get('field_more_information')->format);
          }
          break;
      }

      // Teaser image
      switch ($view_mode) {
        case 'teaser':
        case 'content_hub_teaser':
          $mhr_node_variables['teaser_image'] = null;
          if (
            $node->hasField('field_meta_image') &&
            $node->field_meta_image->isEmpty() == FALSE
          ) {
            $teaser_image_data = _mhr_get_image_data($node->field_meta_image);
            if ($teaser_image_data) {
              $mhr_node_variables['teaser_image'] = [
                '#height' => $teaser_image_data['image_height'],
                '#responsive_image_style_id' => 'event_teaser',
                '#theme' => 'responsive_image',
                '#uri' => $teaser_image_data['image_uri'],
                '#width' => $teaser_image_data['image_width'],
              ];
            }
          }
          break;
      }

      // Link to post
      switch ($view_mode) {
        case 'teaser':
        case 'content_hub_teaser':
          $mhr_node_variables['link_to_post'] = [
            'label' => $mhr_node_variables['title'],
            'url' => Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => TRUE]),
          ];
          break;
      }

      // Set up past events shown at bottom of event pages.
      $mhr_past_events_carousel_render_array = null;
      switch ($view_mode) {
        case 'full':
          // Get the items to be displayed.
          $mhr_past_events_view_results = views_get_view_result('mhr_past_events', 'default', $variables['node']->id());

          // If there are results, build markup for them, and create a carousel.
          if (count($mhr_past_events_view_results) > 0) {
            $mhr_past_events_cards = [];
            $viewBuilder = \Drupal::entityTypeManager()->getViewBuilder('node');
            foreach($mhr_past_events_view_results as $mhr_past_events_view_results_single) {
              // Get a render array for the result's node, in 'teaser' display.
              // This will run the nodes through the node--event--teaser.html.twig template.
              $mhr_past_events_cards[] = $viewBuilder->view($mhr_past_events_view_results_single->_entity, 'teaser');
            }
            // Set up carousel settings for Swiper.
            $carousel_settings = json_encode([
              'slidesPerGroup' => 1,
              'slidesPerView' => 1,
              'spaceBetween' => 24,
              'speed' => 250,
              'watchSlidesProgress' => true,
              'breakpoints' => [
                '460' => [
                  'slidesPerGroup' => 1,
                  'slidesPerView' => 1,
                ],
                '768' => [
                  'slidesPerGroup' => 2,
                  'slidesPerView' => 2,
                ],
                '992' => [
                  'slidesPerGroup' => 3,
                  'slidesPerView' => 3,
                ],
                '1200' => [
                  'slidesPerGroup' => 3,
                  'slidesPerView' => 3,
                ],
              ],
            ]);
            $mhr_past_events_carousel_render_array = [
              '#theme' => 'vsc_carousel_grid',
              '#carousel_items' => $mhr_past_events_cards,
              '#carouselSettings' => $carousel_settings,
              '#vsc_carousel_prev_icon' => 'arrow-right',
              '#vsc_carousel_next_icon' => 'arrow-right',
            ];
            $mhr_past_events_carousel_render_array['#attached']['library'][] = 'mhr/mhr-carousel';
          }
          break;
      }
      $variables['mhr_past_events_on_event_page'] = $mhr_past_events_carousel_render_array;

      // field_brochure / field_highlight_text_button / field_file_download_title
      // *** NOT USED in any pages

      // field_start_date
      $field_start_date = null;
      if (
        $node->hasField('field_start_date') &&
        $node->field_start_date->isEmpty() == FALSE
      ) {
        $field_start_date_object = new DrupalDateTime($node->field_start_date->value);
        $field_start_date = _mhr_fuzzy_date_for_teaser($field_start_date_object->getTimestamp());
      }
      $mhr_node_variables['start_date_for_teaser'] = $field_start_date;

      // Add node data to variables
      $variables['mhr_event'] = $mhr_node_variables;
      break;

    case 'flexipage':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_flexipage'] = $mhr_node_variables;
      break;

    case 'form_page':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // Whether to hide the title
      $mhr_node_variables['hide_title'] = $node->get('field_hide_title')->value;

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_form_page'] = $mhr_node_variables;
      break;

    case 'knowledge_hub_page':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // Main views block
      $mhr_node_variables['knowledge_hub_view_block'] = views_embed_view('content_hub', 'knowledge_hub_block');

      // Add node data to variables
      $variables['mhr_knowledge_hub_page'] = $mhr_node_variables;
      break;

    case 'listing':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_listing_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_listing'] = $mhr_node_variables;
      break;

    case 'mhr_knowledge_hub_landing_page':
      // Get the node
      $node = $variables['node'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['mhr_knowledge_hub_landing_page_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_knowledge_hub_landing_page'] = $mhr_node_variables;
      break;

    case 'mhr_partner_integration':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // We're only interested in the 'teaser' view mode for this content type.
      if ($view_mode === 'teaser') {

        // Set up array for holding node data
        $mhr_node_variables = [];

        // Title
        $mhr_node_variables['mhr_partner_integration_title'] = $node->getTitle();

        // field_mhr_partner_logo
        $mhr_node_variables['mhr_partner_integration_logo'] = null;
        if (
          $node->hasField('field_mhr_partner_logo') &&
          $node->field_mhr_partner_logo->isEmpty() == FALSE
        ) {
          $field_mhr_partner_logo_data = _mhr_get_image_data($node->field_mhr_partner_logo);
          if ($field_mhr_partner_logo_data) {
            $mhr_node_variables['mhr_partner_integration_logo'] = [
              '#theme' => 'image_style',
              '#uri' => $field_mhr_partner_logo_data['image_uri'],
              '#style_name' => 'mhr_partner_integration_logo',
              '#alt' => $node->get('field_mhr_partner_logo')->entity->get('field_media_image')->alt,
            ];
          }
        }

        // Set up the industry and services string
        $mhr_node_variables['mhr_partner_integration_industry_services'] = '';
        // MHR003-504 - Removed showing Industry as Megan said:
        // the Industry tags shouldn’t feature on the cards, i.e. ‘Banking, Finance & Insurance; Hotels, Leisure and entertainment. It should only be the services, i.e; Finance; Payroll.
        // field_mhr_partner_industry
//        if (
//          $node->hasField('field_mhr_partner_industry') &&
//          $node->field_mhr_partner_industry->isEmpty() == FALSE
//        ) {
//          $field_mhr_partner_industry_data = $node->field_mhr_partner_industry->getValue();
//          if ($field_mhr_partner_industry_data) {
//            $field_mhr_partner_industry_data_ids = array_column($field_mhr_partner_industry_data, 'target_id');
//            foreach ($field_mhr_partner_industry_data_ids as $field_mhr_partner_industry_id) {
//              if (!empty($mhr_node_variables['mhr_partner_integration_industry_services'])) {
//                $mhr_node_variables['mhr_partner_integration_industry_services'] .= '; ';
//              }
//              $mhr_node_variables['mhr_partner_integration_industry_services'] .= Term::load($field_mhr_partner_industry_id)->get('name')->value;
//            }
//          }
//        }
        // field_mhr_partner_services
        if (
          $node->hasField('field_mhr_partner_services') &&
          $node->field_mhr_partner_services->isEmpty() == FALSE
        ) {
          $field_mhr_partner_services_data = $node->field_mhr_partner_services->getValue();
          if ($field_mhr_partner_services_data) {
            $field_mhr_partner_services_data_ids = array_column($field_mhr_partner_services_data, 'target_id');
            foreach ($field_mhr_partner_services_data_ids as $field_mhr_partner_services_id) {
              if (!empty($mhr_node_variables['mhr_partner_integration_industry_services'])) {
                $mhr_node_variables['mhr_partner_integration_industry_services'] .= '; ';
              }
              $mhr_node_variables['mhr_partner_integration_industry_services'] .= Term::load($field_mhr_partner_services_id)->get('name')->value;
            }
          }
        }

        // field_mhr_partner_teaser_subhead
        $mhr_node_variables['mhr_partner_integration_teaser_subhead'] = null;
        if (
          $node->hasField('field_mhr_partner_teaser_subhead') &&
          $node->field_mhr_partner_teaser_subhead->isEmpty() == FALSE
        ) {
          $mhr_node_variables['mhr_partner_integration_teaser_subhead'] = strip_tags($node->field_mhr_partner_teaser_subhead->value);
        }

        // field_mhr_partner_teaser_text
        $mhr_node_variables['mhr_partner_integration_teaser_text'] = null;
        if (
          $node->hasField('field_mhr_partner_teaser_text') &&
          $node->field_mhr_partner_teaser_text->isEmpty() == FALSE
        ) {
          $mhr_node_variables['mhr_partner_integration_teaser_text'] = [
            '#type' => 'inline_template',
            '#template' => $node->field_mhr_partner_teaser_text->value,
          ];
        }

        // field_mhr_partner_has_full_page
        $mhr_node_variables['mhr_partner_integration_has_full_page'] = FALSE;
        if (
          $node->hasField('field_mhr_partner_has_full_page') &&
          $node->field_mhr_partner_has_full_page->isEmpty() == FALSE &&
          $node->field_mhr_partner_has_full_page->value === 'mhr_partner_full_page_yes'
        ) {
          $mhr_node_variables['mhr_partner_integration_has_full_page'] = TRUE;
        }

        // field_mhr_partner_full_page
        $mhr_node_variables['mhr_partner_integration_full_page_url'] = null;
        if (
          $node->hasField('field_mhr_partner_full_page') &&
          $node->field_mhr_partner_full_page->isEmpty() == FALSE
        ) {
          // We're presuming field_mhr_partner_full_page only accepts a single value,
          // so just get the first one here.
          $field_mhr_partner_full_page_data = $node->field_mhr_partner_full_page->getValue();
          if ($field_mhr_partner_full_page_data) {
            $field_mhr_partner_full_page_data_ids = array_column($field_mhr_partner_full_page_data, 'target_id');
            $field_mhr_partner_full_page_data_id = reset($field_mhr_partner_full_page_data_ids);
            $mhr_node_variables['mhr_partner_integration_full_page_url'] = \Drupal::service('path_alias.manager')->getAliasByPath('/node/' . $field_mhr_partner_full_page_data_id);
          }
        }

        // Add node data to variables
        $variables['mhr_partner_integration'] = $mhr_node_variables;
      }
      break;

    case 'podcast_listing_item':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // body
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['body'] = $node->body->view([
            'label' => 'hidden',
            'type' => 'smart_trim',
            'settings' => [
              'summary_handler' => 'trim',
              'trim_length' => 30,
              'trim_suffix' => '…',
              'trim_type' => 'words',
              'trim_options' => [
                'text' => true,
              ],
            ],
          ]);
          break;
      }

      // title
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['title'] = $node->getTitle();
          break;
      }

      // image
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['image'] = null;
          if (
            $node->hasField('field_resource_image') &&
            $node->field_resource_image->isEmpty() == FALSE
          ) {
            $article_image_data = _mhr_get_image_data($node->field_resource_image);
            if ($article_image_data) {
              $mhr_node_variables['image'] = [
                '#height' => $article_image_data['image_height'],
                '#responsive_image_style_id' => 'content_hub_card_teaser',
                '#theme' => 'responsive_image',
                '#uri' => $article_image_data['image_uri'],
                '#width' => $article_image_data['image_width'],
              ];
            }
          }
          break;
      }

      // button
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // field_published_date
      $field_published_date = null;
      if (
        $node->hasField('field_published_date') &&
        $node->field_published_date->isEmpty() == FALSE
      ) {
        $field_published_date_object = new DrupalDateTime($node->field_published_date->value);
        $field_published_date = _mhr_fuzzy_date_for_teaser($field_published_date_object->getTimestamp());
      }
      $mhr_node_variables['published_date_for_teaser'] = $field_published_date;

      // Add node data to variables
      $variables['mhr_podcast_listing_item'] = $mhr_node_variables;
      break;

    case 'pillar_page':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // created_date
      $mhr_node_variables['created_date'] = date('j F Y', $node->created->value);

      // created_date_for_teaser
      $mhr_node_variables['created_date_for_teaser'] = _mhr_fuzzy_date_for_teaser($node->created->value);

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // Image
      $mhr_node_variables['image'] = null;
      $mhr_node_variables['image_no_lazy'] = null;

      if (!$node->get('field_image')->isEmpty() &&
          !$node->get('field_image')->entity->get('field_media_image')->isEmpty()
        ) {
        $file = File::load($node->get('field_image')->entity->get('field_media_image')->entity->id());
        $uri = $file->getFileUri();

        // Common image build array
        $image_build_base = [
          '#theme' => 'responsive_image',
          '#uri' => $uri,
          '#responsive_image_style_id' => 'product_image',
          '#attributes' => [
            'alt' => $node->get('field_image')->entity->get('field_media_image')->alt,
          ],
        ];

        // Image with lazy loading
        $image_build_lazy = $image_build_base;
        $image_build_lazy['#attributes']['loading'] = 'lazy';
        $mhr_node_variables['image'] = \Drupal::service('renderer')->render($image_build_lazy);

        // Image without lazy loading (loading eager)
        $image_build_no_lazy = $image_build_base;
        $image_build_no_lazy['#attributes']['loading'] = 'eager';
        $mhr_node_variables['image_no_lazy'] = \Drupal::service('renderer')->render($image_build_no_lazy);
      }


      // field_pillar_description
      switch ($view_mode) {
        case 'content_hub_teaser':
          $mhr_node_variables['body'] = null;
          if (
            $node->hasField('field_pillar_description') &&
            $node->field_pillar_description->isEmpty() == FALSE
          ) {
            $mhr_node_variables['body'] = $node->field_pillar_description->view([
              'label' => 'hidden',
              'type' => 'smart_trim',
              'settings' => [
                'summary_handler' => 'trim',
                'trim_length' => 30,
                'trim_suffix' => '…',
                'trim_type' => 'words',
                'trim_options' => [
                  'text' => true,
                ],
              ],
            ]);
          }
          break;
        case 'full':
          $mhr_node_variables['pillar_description'] = null;
          if (
            $node->hasField('field_pillar_description') &&
            $node->field_pillar_description->isEmpty() == FALSE
          ) {
            $mhr_node_variables['pillar_description'] = [
              '#type' => 'processed_text',
              '#text' => $node->field_pillar_description->value,
              '#format' => $node->field_pillar_description->format,
            ];
          }
          break;
      }

      switch ($view_mode) {
        case 'full':
          // Structure content body - pass individual content sections to template to output.
          // field_pillar_content contains the 'Content section' references.
          $field_pillar_content = null;
          if (
            $node->hasField('field_pillar_content') &&
            $node->field_pillar_content->isEmpty() == FALSE
          ) {
            $field_pillar_content = [];
            $field_pillar_content_sections = $node->field_pillar_content->referencedEntities();
            foreach($field_pillar_content_sections as $key => $field_pillar_content_section) {
              // field_content_section_hide_title
              $field_content_section_hide_title = FALSE;
              if (
                $field_pillar_content_section->hasField('field_content_section_hide_title') &&
                $field_pillar_content_section->field_content_section_hide_title->isEmpty() == FALSE &&
                $field_pillar_content_section->field_content_section_hide_title->value === 'hide'
              ) {
                $field_content_section_hide_title = TRUE;
              }
              // field_content_section_title
              $field_content_section_title = null;
              if ($field_content_section_hide_title === FALSE) {
                if (
                  $field_pillar_content_section->hasField('field_content_section_title') &&
                  $field_pillar_content_section->field_content_section_title->isEmpty() == FALSE
                ) {
                  $field_content_section_title = $field_pillar_content_section->field_content_section_title->value;
                } else {
                  $field_content_section_title = 'Section ' . ($key + 1);
                }
              }
              // field_content_section_row
              $field_content_section_row = null;
              if (
                $field_pillar_content_section->hasField('field_content_section_row') &&
                $field_pillar_content_section->field_content_section_row->isEmpty() == FALSE
              ) {
                $field_content_section_row = $field_pillar_content_section->field_content_section_row->view([
                  'label' => 'hidden',
                  'type' => 'layout_paragraphs',
                ]);
              }
              $field_pillar_content[] = [
                'mhr_pillar_page_content_section_id' => $field_pillar_content_section->id(),
                'mhr_pillar_page_content_section_title' => $field_content_section_title,
                'mhr_pillar_page_content_section_body' => $field_content_section_row,
              ];
            }
          }
          $variables['mhr_pillar_page_content_sections'] = $field_pillar_content;
          break;
      }

      // Link to post
      switch ($view_mode) {
        case 'content_hub_teaser':
          $mhr_node_variables['link_to_post'] = [
            'label' => $mhr_node_variables['title'],
            'url' => Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => TRUE]),
          ];
          break;
      }

      // Add node data to variables
      $variables['mhr_pillar_page'] = $mhr_node_variables;
      break;

    case 'product':

      // Set up array for holding node data
      $mhr_node_variables = [];

      // Row content
      // Get the paragraphs from the field and render as layout paragraphs
      $layout_paragraphs_output = $node->get('field_product_rows')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
      $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);

      // Add node data to variables
      $variables['mhr_product'] = $mhr_node_variables;
      break;

    case 'video_listing_item':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // body
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['body'] = $node->body->view([
            'label' => 'hidden',
            'type' => 'smart_trim',
            'settings' => [
              'summary_handler' => 'trim',
              'trim_length' => 30,
              'trim_suffix' => '…',
              'trim_type' => 'words',
              'trim_options' => [
                'text' => true,
              ],
            ],
          ]);
          break;
      }

      // title
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['title'] = $node->getTitle();
          break;
      }

      // image
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['image'] = null;
          if (
            $node->hasField('field_resource_image') &&
            $node->field_resource_image->isEmpty() == FALSE
          ) {
            $article_image_data = _mhr_get_image_data($node->field_resource_image);
            if ($article_image_data) {
              $mhr_node_variables['image'] = [
                '#height' => $article_image_data['image_height'],
                '#responsive_image_style_id' => 'content_hub_card_teaser',
                '#theme' => 'responsive_image',
                '#uri' => $article_image_data['image_uri'],
                '#width' => $article_image_data['image_width'],
              ];
            }
          }
          break;
      }

      // button
      switch ($view_mode) {
        case 'content_hub_teaser':
        case 'teaser':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // field_published_date
      $field_published_date = null;
      if (
        $node->hasField('field_published_date') &&
        $node->field_published_date->isEmpty() == FALSE
      ) {
        $field_published_date_object = new DrupalDateTime($node->field_published_date->value);
        $field_published_date = _mhr_fuzzy_date_for_teaser($field_published_date_object->getTimestamp());
      }
      $mhr_node_variables['published_date_for_teaser'] = $field_published_date;

      // Add node data to variables
      $variables['mhr_video_listing_item'] = $mhr_node_variables;
      break;

    case 'webinar':

      // Get view mode
      $view_mode = $variables['view_mode'];

      // Set up array for holding node data
      $mhr_node_variables = [];

      // created_date
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['created_date'] = date('j F Y', $node->created->value);
          break;
      }

      // page title
      $mhr_node_variables['title'] = $node->getTitle();

      // description
      $mhr_node_variables['description'] = null;
      switch($view_mode) {
        case 'full':
          if (!$node->get('field_description')->isEmpty()) {
            $mhr_node_variables['description'] = check_markup($node->get('field_description')->value, $node->get('field_description')->format);
          }
          break;
        case 'teaser':
        case 'content_hub_teaser':
          if (
            $node->hasField('field_description') &&
            $node->field_description->isEmpty() == FALSE
          ) {
            $mhr_node_variables['description'] = $node->field_description->view([
              'label' => 'hidden',
              'type' => 'smart_trim',
              'settings' => [
                'summary_handler' => 'trim',
                'trim_length' => 30,
                'trim_suffix' => '…',
                'trim_type' => 'words',
                'trim_options' => [
                  'text' => true,
                ],
              ],
            ]);
          }
          break;
      }

      // highlights
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['highlights'] = null;
          if (!$node->get('field_webinar_highlights')->isEmpty()) {
            $mhr_node_variables['highlights'] = check_markup($node->get('field_webinar_highlights')->value, $node->get('field_webinar_highlights')->format);
          }
          break;
      }

      // Row content
      switch($view_mode) {
        case 'full':
          // Get the paragraphs from the field and render as layout paragraphs
          $layout_paragraphs_output = $node->get('field_rows_webinar')->view(['label' => 'hidden', 'type' => 'layout_paragraphs']);
          $mhr_node_variables['structured_content_rows'] = \Drupal::service('renderer')->render($layout_paragraphs_output);
          break;
      }

      // Background image
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['background_image'] = null;
          if (!empty($node->get('field_background_image')->entity->get('field_media_image')->entity->id())) {
            $file = File::load($node->get('field_background_image')->entity->get('field_media_image')->entity->id());
            $uri = $file->getFileUri();
            $background_image_display_mode = 'hero_banner';
            if (
              $node->hasField('field_hero_banner_depth') &&
              $node->field_hero_banner_depth->isEmpty() == FALSE &&
              $node->field_hero_banner_depth->value == 'shallow'
            ) {
              $background_image_display_mode = 'hero_banner_shallow';
            }
            $image_build = [
              '#theme' => 'responsive_image',
              '#uri' => $uri,
              '#responsive_image_style_id' => $background_image_display_mode,
              '#attributes' => [
                'alt' => $node->get('field_background_image')->entity->get('field_media_image')->alt,
              ]
            ];
            $mhr_node_variables['background_image'] = \Drupal::service('renderer')
              ->render($image_build);
          }
          break;
      }

      // button
      switch($view_mode) {
        case 'full':
          $mhr_node_variables['button'] = null;
          $button = $node->get('field_button')->first();
          if ($button) {
            $mhr_node_variables['button'] = [
              'title' => $button->title,
              'link' => $button->getUrl()->toString()
            ];
          }
          break;
      }

      // Time zone description
      switch($view_mode) {
        case 'full':
          $hide_time = (!$node->get('field_hide_time')->isEmpty()) ? $node->get('field_hide_time')->value : false;
          $mhr_node_variables['time_zone'] = null;
          if (!$hide_time) {
            // Get the allowed list values for the field.
            $field_definition = $node->getFieldDefinition('field_time_zone');
            $allowed_values = $field_definition->getFieldStorageDefinition()->getSetting('allowed_values');
            // Get the label for the selected value.
            $mhr_node_variables['time_zone'] = isset($allowed_values[$node->get('field_time_zone')->value]) ? $allowed_values[$node->get('field_time_zone')->value] : $node->get('field_time_zone')->value;
          }
          break;
      }

      // Teaser image
      switch ($view_mode) {
        case 'teaser':
        case 'content_hub_teaser':
          $mhr_node_variables['teaser_image'] = null;
          if (
            $node->hasField('field_meta_image') &&
            $node->field_meta_image->isEmpty() == FALSE
          ) {
            $teaser_image_data = _mhr_get_image_data($node->field_meta_image);
            if ($teaser_image_data) {
              $mhr_node_variables['teaser_image'] = [
                '#height' => $teaser_image_data['image_height'],
                '#responsive_image_style_id' => 'event_teaser',
                '#theme' => 'responsive_image',
                '#uri' => $teaser_image_data['image_uri'],
                '#width' => $teaser_image_data['image_width'],
              ];
            }
          }
          break;
      }

      // Link to post
      switch ($view_mode) {
        case 'teaser':
        case 'content_hub_teaser':
          $mhr_node_variables['link_to_post'] = [
            'label' => $mhr_node_variables['title'],
            'url' => Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => TRUE]),
          ];
          break;
      }

      // Set up past webinars shown at bottom of event pages.
      $mhr_past_webinars_carousel_render_array = null;
      switch ($view_mode) {
        case 'full':
          // Get the items to be displayed.
          $mhr_past_webinars_view_results = views_get_view_result('mhr_past_webinars', 'default', $variables['node']->id());

          // If there are results, build markup for them, and create a carousel.
          if (count($mhr_past_webinars_view_results) > 0) {
            $mhr_past_webinars_cards = [];
            $viewBuilder = \Drupal::entityTypeManager()->getViewBuilder('node');
            foreach($mhr_past_webinars_view_results as $mhr_past_webinars_view_results_single) {
              // Get a render array for the result's node, in 'teaser' display.
              // This will run the nodes through the node--webinar--teaser.html.twig template.
              $mhr_past_webinars_cards[] = $viewBuilder->view($mhr_past_webinars_view_results_single->_entity, 'teaser');
            }
            // Set up carousel settings for Swiper.
            $carousel_settings = json_encode([
              'slidesPerGroup' => 1,
              'slidesPerView' => 1,
              'spaceBetween' => 24,
              'speed' => 250,
              'watchSlidesProgress' => true,
              'breakpoints' => [
                '768' => [
                  'slidesPerGroup' => 2,
                  'slidesPerView' => 2,
                ],
                '992' => [
                  'slidesPerGroup' => 2,
                  'slidesPerView' => 2,
                ],
                '1200' => [
                  'slidesPerGroup' => 3,
                  'slidesPerView' => 3,
                ],
              ],
            ]);
            $mhr_past_webinars_carousel_render_array = [
              '#theme' => 'vsc_carousel_grid',
              '#carousel_items' => $mhr_past_webinars_cards,
              '#carouselSettings' => $carousel_settings,
              '#vsc_carousel_prev_icon' => 'arrow-right',
              '#vsc_carousel_next_icon' => 'arrow-right',
            ];
            $mhr_past_webinars_carousel_render_array['#attached']['library'][] = 'mhr/mhr-carousel';
          }
          break;
      }
      $mhr_node_variables['past_webinars'] = $mhr_past_webinars_carousel_render_array;

      // field_webinar_start_date
      $field_webinar_start_date = null;
      if (
        $node->hasField('field_webinar_start_date') &&
        $node->field_webinar_start_date->isEmpty() == FALSE
      ) {
        $field_webinar_start_date_object = new DrupalDateTime($node->field_webinar_start_date->value);
        $field_webinar_start_date = _mhr_fuzzy_date_for_teaser($field_webinar_start_date_object->getTimestamp());
      }
      $mhr_node_variables['start_date_for_teaser'] = $field_webinar_start_date;

      // Add node data to variables
      $variables['mhr_webinar'] = $mhr_node_variables;
      break;
  }
}

/*
 * Implements hook_preprocess_views_view_fields().
 *
 */
function mhr_contenttypes_preprocess_preprocess_views_view_fields(&$variables) {

  switch($variables['view']->id()) {

    case 'search':

      // Set up variables for use in the search result template, views-view-fields--search.html.twig.

      /**
       * @var MHRContentTypeUtilities
       */
      $mhr_content_type_utilities = \Drupal::service('mhr_content_type_utilities');

      $variables_for_template = [];

      // Load the node associated with the search result.
      // This presumes that the search result *is* a node, of course. If the 'search' View changes so that this
      // is not the case, this code will no longer work.
      if (isset($variables['row']->_entity)) {

        $result_node = $variables['row']->_entity;

        // Content type tag.
        $variables_for_template['mhr_search_result_content_type_tag'] = $mhr_content_type_utilities->getMHRPublicContentTypeLabel($result_node);

        // Last updated time/date.
        $variables_for_template['mhr_search_result_last_updated_timestamp'] = $result_node->getChangedTime();

        // Link for search result.
        $variables_for_template['mhr_search_result_link'] = \Drupal::service('path_alias.manager')->getAliasByPath('/node/' . $result_node->id());

        // Title for search result.
        $result_title = $result_node->label();
        // Some node titles have (UK), (US) or (IE) at the start of their titles to help distinguish them on
        // admin pages. This is removed here as they do not make a lot of sense to output within search
        // results.
        if (substr($result_title, 0, 4) == '(IE)') {
          $result_title = substr($result_title, 4);
        }
        if (substr($result_title, 0, 4) == '(UK)') {
          $result_title = substr($result_title, 4);
        }
        if (substr($result_title, 0, 4) == '(US)') {
          $result_title = substr($result_title, 4);
        }
        $variables_for_template['mhr_search_result_title'] = $result_title;

        // Snippet for search result.
        $variables_for_template['mhr_search_result_snippet'] = null;
        foreach($variables['view']->field as $id => $field) {
          if ($id === 'search_api_excerpt') {
            $variables_for_template['mhr_search_result_snippet'] = [
              '#type' => 'inline_template',
              '#template' => $variables['fields']['search_api_excerpt']->raw,
            ];
          }
        }
      }

      $variables['mhr_search_result'] = $variables_for_template;
      break;
  }
}

/**
 * Helper function to format a date to be shown on teasers, to either show the date
 * in a standard format, or to show other text in some situations.
 *
 * @param string $timestamp
 *   Unix timestamp representing the date to be formatted.
 *
 * @return string
 *   Formatted date.
 */
function _mhr_fuzzy_date_for_teaser($timestamp = NULL): string {

  if ($timestamp === NULL) {
    return '';
  }

  // Create default formatted date.
  $to_return = date('j F Y', $timestamp);

  // If the date is more than a year ago, replace the formatted date with text.
  if ($timestamp < strtotime('-2 year')) {
    $to_return = 'More than two years ago';
  }

  return $to_return;
}

/**
 * Helper function to convert the 'Estimated reading time' field value from minutes into
 * minutes and hours.
 *
 * @param Node $node
 *   A node object.
 *
 * @return mixed
 *   String containing the estimated reading time in minutes, or NULL if there is no value
 *   available.
 */
function _mhr_get_estimated_reading_time($node = NULL): mixed {

  // Check a value for $node has been supplied.
  if ($node === NULL) {
    return NULL;
  }

  // Get the value of the estimated reading time field.
  if (
    $node->hasField('field_mhr_estimated_reading_time') &&
    $node->field_mhr_estimated_reading_time->isEmpty() == FALSE
  ) {
    // We have a value.
    $field_mhr_estimated_reading_time = $node->field_mhr_estimated_reading_time->value;
    // Check if the value is zero or less, in which case return nothing.
    if ($field_mhr_estimated_reading_time <= 0) {
      return NULL;
    }
    // Return the value in minutes.
    return $field_mhr_estimated_reading_time;
  }

  // Fallback.
  return NULL;
}
