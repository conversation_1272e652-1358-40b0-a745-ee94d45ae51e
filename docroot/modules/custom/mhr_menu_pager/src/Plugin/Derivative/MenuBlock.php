<?php

namespace Drupal\mhr_menu_pager\Plugin\Derivative;

use <PERSON><PERSON>al\Component\Plugin\Derivative\DeriverBase;
use <PERSON><PERSON>al\Core\Entity\EntityStorageInterface;
use <PERSON>upal\Core\Plugin\Discovery\ContainerDeriverInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use Drupal\Core\StringTranslation\TranslationInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides block plugin definitions for custom menus.
 *
 * @see \Drupal\mhr_menu_pager\Plugin\Block\MenuBlock
 */
class MenuBlock extends DeriverBase implements ContainerDeriverInterface {
  use StringTranslationTrait;
  /**
   * The menu storage.
   *
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  protected $menuStorage;

  /**
   * Constructs new MenuBlock.
   *
   * @param \Drupal\Core\Entity\EntityStorageInterface $menu_storage
   *   The menu storage.
   * @param \Drupal\Core\StringTranslation\TranslationInterface $string_translation
   *   String translation service.
   */
  public function __construct(EntityStorageInterface $menu_storage, TranslationInterface $string_translation) {
    $this->menuStorage = $menu_storage;
    $this->stringTranslation = $string_translation;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, $base_plugin_id) {
    return new static(
      $container->get('entity_type.manager')->getStorage('menu'),
      $container->get('string_translation')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getDerivativeDefinitions($base_plugin_definition) {
    foreach ($this->menuStorage->loadMultiple() as $menu => $entity) {
      $this->derivatives[$menu] = $base_plugin_definition;
      $this->derivatives[$menu]['admin_label'] = $this->t('MHR Menu Pager - @label', ['@label' => $entity->label()]);
      $this->derivatives[$menu]['config_dependencies']['config'] = [$entity->getConfigDependencyName()];
    }
    return $this->derivatives;
  }

}
