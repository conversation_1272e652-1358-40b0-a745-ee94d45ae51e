# MHR Menu Pager

## CONTENTS OF THIS FILE

 * Introduction
 * Requirements
 * Installation
 * Configuration


## INTRODUCTION

MHR Menu Pager adds a block for each menu that adds previous and next link
navigation based on the current page.

It does this by flattening the menu tree to calculate which links should
come before and after the menu link corresponding to the current page.
This can optionally be restricted to links with the same parent as the
active menu link (by default, it uses traverses the entire menu tree).

This is a custom module for MHR based on the Menu Pager contrib module.


## REQUIREMENTS

No special requirements.


## INSTALLATION

 * Install as you would normally install a custom Drupal module.
   See: https://www.drupal.org/node/895232 for further information.


## CONFIGURATION

No configuration is needed.
