<?php

/**
 * @file
 * MHR Menu Pager block for each menu with previous and next link navigation.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_mhr_menu_pager_ignore_paths().
 */
function mhr_menu_pager_mhr_menu_pager_ignore_paths($menu_name) {
  $paths = [];
  // Ignore Special Menu Items paths that don't actually link to anything.
  $paths[] = '<nolink>';
  $paths[] = '<separator>';

  return $paths;
}

/**
 * Implements hook_theme().
 */
function mhr_menu_pager_theme($existing, $type, $theme, $path) {
  return [
    'mhr_menu_pager' => [
      'variables' => [
        'previous' => NULL,
        'next' => NULL,
      ],
    ],
  ];
}

/**
 * Implements hook_help().
 */
function mhr_menu_pager_help($route_name, RouteMatchInterface $route_match) {
  if ($route_name === 'help.page.mhr_menu_pager') {
    $readme_file = file_exists(__DIR__ . '/README.md') ? __DIR__ . '/README.md' : __DIR__ . '/README.txt';
    if (!file_exists($readme_file)) {
      return NULL;
    }
    $text = file_get_contents($readme_file);
    if (!\Drupal::moduleHandler()->moduleExists('markdown')) {
      return '<pre>' . $text . '</pre>';
    }
    else {
      // Use the Markdown filter to render the README.
      $filter_manager = \Drupal::service('plugin.manager.filter');
      $settings = \Drupal::configFactory()->get('markdown.settings')->getRawData();
      $config = ['settings' => $settings];
      $filter = $filter_manager->createInstance('markdown', $config);
      return $filter->process($text, 'en');
    }
  }
  return NULL;
}
