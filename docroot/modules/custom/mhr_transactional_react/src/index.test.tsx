import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { mockPricingData } from './data/mockPricing';
import { PricingData } from './types';

describe('Price Calculator Initialization', () => {
  let originalInitPriceCalculator: typeof window.initPriceCalculator;

  beforeEach(() => {
    originalInitPriceCalculator = window.initPriceCalculator;
    document.body.innerHTML = '';
    window.initPriceCalculator = () => Promise.resolve();
    jest.isolateModules(() => {
      require('./index');
    });
  });

  afterEach(() => {
    if (originalInitPriceCalculator) {
      window.initPriceCalculator = originalInitPriceCalculator;
    }
  });

  it('initializes calculator when called with valid container', async () => {
    const container = document.createElement('div');
    container.id = 'test-calculator';
    document.body.appendChild(container);

    await act(async () => {
      await window.initPriceCalculator('test-calculator');
    });

    // Wait for step text to appear
    const stepText = await screen.findByText('1. Number of Employees');
    expect(stepText).toBeInTheDocument();
  });

  it('accepts custom pricing data', async () => {
    const container = document.createElement('div');
    container.id = 'test-calculator';
    document.body.appendChild(container);

    const customPricing: PricingData = {
      currency: {
        code: 'GBP',
        symbol: '£'
      },
      employeeLimits: {
        min: 1,
        max: 99,
        default: 50
      },
      packages: [{
        name: 'Custom Package',
        price: 10,
        features: ['Feature 1']
      }],
      addOns: [],
      quickStart: []
    };

    await act(async () => {
      await window.initPriceCalculator('test-calculator', customPricing);
    });

    // Wait for package heading to appear
    const packageHeading = await screen.findByRole('heading', { name: 'Custom Package' });
    expect(packageHeading).toBeInTheDocument();
  });

  it('handles invalid container ID gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'error');
    
    // Try to initialize with non-existent container
    window.initPriceCalculator('non-existent');

    // Should not throw error
    expect(consoleSpy).not.toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });
}); 