import { useState, useEffect } from 'react';
import { Step, CustomerData, SelectedAddOns } from '../types';
import { submitCalculatorForm } from '../services/api';
import { isResetMode } from '../utils/urlUtils';

interface FormState {
  employeeCount: number;
  selectedPackage: string;
  addOns: SelectedAddOns;
  includeQuickStart: boolean;
  customerData?: CustomerData;
  currentStep: Step;
  completedSteps: Step[];
}

/**
 * Maps backend order data to React form state
 */
const mapOrderToFormState = (order: any): Partial<FormState> => {
  if (!order) return {};

  // Map backend order data to React form state
  return {
    employeeCount: parseInt(order.number_employees) || 50,
    selectedPackage: order.package1 === '1' ? 'hr_only' : 'hr_payroll',
    addOns: {
      recruitment: order.recruitment_addon === '1'
    },
    includeQuickStart: Boolean(order.assist1 === '1' || order.assist2 === '1'),
    customerData: order.email ? {
      firstName: order.first_name || '',
      lastName: order.last_name || '',
      email: order.email || '',
      confirmEmail: order.email || '',
      jobTitle: order.job_title || '',
      contactNumber: order.contact_number || '',
      companyName: order.company_name || '',
      companyRegistrationNumber: order.company_registration_number || '',
      addressLine1: order.company_address_1 || '',
      addressLine2: order.company_address_2 || '',
      town: order.company_town || '',
      postcode: order.company_postcode || '',
      country: order.company_country || ''
    } : undefined,
    currentStep: order.step ?
      (order.step === 'selection' ? Step.SELECTION :
       order.step === 'data_capture' ? Step.DATA_CAPTURE :
       order.step === 'check' ? Step.COMPLIANCE :
       order.step === 'payment' ? Step.PAYMENT :
       order.step === 'thank_you' ? Step.THANK_YOU : Step.SELECTION)
      : Step.SELECTION,
    completedSteps: []
  };
};

export const useFormState = () => {
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [formState, setFormState] = useState<FormState>({
    employeeCount: 50,
    selectedPackage: 'HR Only',
    addOns: {
      recruitment: false
    },
    includeQuickStart: false,
    currentStep: Step.SELECTION,
    completedSteps: [],
  });

  // Only handle initial load and reload requests
  useEffect(() => {
    console.log("useEffect - initial load or reload");

    // Check if we're in reset mode
    if (isResetMode()) {
      console.log("Reset mode detected, resetting form state");
      resetFormState();
      return; // Skip the normal initialization
    }

    if (formState) {
      // Determine if this is a reload request (no actual form changes)
      // Only consider it a reload if we're on the SELECTION step
      const isReload = formState.currentStep === Step.SELECTION && !formState.customerData;

      // Only submit form on initial load or explicit reload, and only for the SELECTION step
      if (isReload) {
        submitCalculatorForm({
          ...formState,
          submissionId: submissionId || undefined,
        }, true).then(response => {
          if (!submissionId) {
            if (response.order && response.order.id) {

              // Is the order paid/complete?
              if (response.order.step === 'paid'
                || response.order.step === 'thank_you'
                || response.order.step === 'complete'
              ) {
                console.log("Resetting form state as previous order is complete");
                resetFormState();
                return;
              }

              console.log('Setting submissionId from response.order.id:', response.order.id);
              setSubmissionId(response.order.id);
            } else if (response.id) {
              console.log('Setting submissionId from response.id:', response.id);
              setSubmissionId(response.id);
            } else {
              console.error('No order ID found in response:', response);
            }
          }

          // If this is a reload request and we have order data, update the form state
          if (response.order) {
            console.log("Updating form state with order data:", response.order);
            const updatedFormState = mapOrderToFormState(response.order);
            // Only update if we have meaningful data
            if (Object.keys(updatedFormState).length > 0) {
              // Skip directly to the step returned from the backend
              if (response.order.step) {
                const stepEnum =
                  response.order.step === 'selection' ? Step.SELECTION :
                  response.order.step === 'data_capture' ? Step.DATA_CAPTURE :
                  response.order.step === 'check' ? Step.COMPLIANCE :
                  response.order.step === 'payment' ? Step.PAYMENT :
                  response.order.step === 'thank_you' ? Step.THANK_YOU :
                  Step.SELECTION;

                // Create a list of completed steps based on the current step
                const completedSteps: Step[] = [];
                if (stepEnum === Step.DATA_CAPTURE || stepEnum === Step.COMPLIANCE || stepEnum === Step.PAYMENT || stepEnum === Step.THANK_YOU) {
                  completedSteps.push(Step.SELECTION);
                }
                if (stepEnum === Step.COMPLIANCE || stepEnum === Step.PAYMENT || stepEnum === Step.THANK_YOU) {
                  completedSteps.push(Step.DATA_CAPTURE);
                }
                if (stepEnum === Step.PAYMENT || stepEnum === Step.THANK_YOU) {
                  completedSteps.push(Step.COMPLIANCE);
                }
                if (stepEnum === Step.THANK_YOU) {
                  completedSteps.push(Step.PAYMENT);
                }

                // Update form state with the correct step and completed steps
                console.log("updating form state here");
                updateFormState({
                  ...updatedFormState,
                  currentStep: stepEnum,
                  completedSteps: completedSteps
                });
              } else {
                updateFormState(updatedFormState);
              }
            }
          }
        }).catch(error => {
          console.error("Error submitting calculator form:", error);
        });
      }
    }
  }, []);  // Only run on initial mount

  const updateFormState = (updates: Partial<FormState>) => {
    console.log("updateFormState");
    setFormState(prev => ({
      ...prev,
      ...updates,
      completedSteps: updates.currentStep
        ? [...prev.completedSteps.filter(step => step !== prev.currentStep), prev.currentStep]
        : prev.completedSteps,
    }));
  };

  const resetFormState = () => {
    setFormState({
      employeeCount: 50,
      selectedPackage: '',
      addOns: {
        recruitment: false
      },
      includeQuickStart: false,
      currentStep: Step.SELECTION,
      completedSteps: [],
      customerData: undefined
    });
    setSubmissionId(null);
  };

  return {
    formState,
    updateFormState,
    resetFormState,
    submissionId,
    setSubmissionId,
  };
};
