export enum Step {
  SELECTION = 'selection',
  DATA_CAPTURE = 'data_capture',
  COMPLIANCE = 'check',
  PAYMENT = 'payment',
  THANK_YOU = 'thank_you',
  ERROR = 'error'
}

export interface DrupalPackage {
  name: string;
  price: number;
}

export interface DrupalAddOn {
  name: string;
  price: number;
}

export interface DrupalPricingConfig {
  packages: {
    hr_only: DrupalPackage;
    hr_payroll: DrupalPackage;
  };
  addons: {
    recruitment: DrupalAddOn;
  };
  quickStart: {
    hr_only: number;
    hr_payroll: number;
  };
  stripe?: {
    publishable_key: string;
  };
  thankyou_url?: string;
  terms_conditions_url?: string;
}

export interface Package {
  name: string;
  price: number;
  features: string[];
  recommended?: boolean;
}

export interface AddOn {
  name: string;
  pricePerEmployee: number;
  description: string;
}

export interface QuickStart {
  name: string;
  price: number;
  description: string;
}

export interface PricingData {
  currency: {
    code: string;
    symbol: string;
  };
  employeeLimits: {
    min: number;
    max: number;
    default: number;
  };
  packages: Package[];
  addOns: AddOn[];
  quickStart: QuickStart[];
}

export interface CustomerData {
  firstName: string;
  lastName: string;
  email: string;
  confirmEmail: string;
  jobTitle: string;
  contactNumber: string;
  companyName: string;
  companyRegistrationNumber: string;
  addressLine1: string;
  addressLine2?: string;
  town: string;
  postcode: string;
  country: string;
  totalAmount?: number;
  transactionId?: string;
}

export interface SelectedAddOns {
  recruitment: boolean;
}
