import { CustomerData } from '../types';

/**
 * Checks if the URL contains the test parameter
 */
export const isTestMode = (): boolean => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('test');
};

/**
 * Checks if the URL contains the reset parameter
 */
export const isResetMode = (): boolean => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('reset');
};

/**
 * Returns test data for contact fields when in test mode
 */
export const getTestData = (): CustomerData => {
  return {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    confirmEmail: '<EMAIL>',
    jobTitle: 'QA Engineer',
    contactNumber: '5555555',
    companyName: 'Test Company Ltd',
    companyRegistrationNumber: '12345678',
    addressLine1: '123 Test Street',
    addressLine2: 'Suite 456',
    town: 'Test Town',
    postcode: 'TE1 1ST',
    country: 'United Kingdom'
  };
};
