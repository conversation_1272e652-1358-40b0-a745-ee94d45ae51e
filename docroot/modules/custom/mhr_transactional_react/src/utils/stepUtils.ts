import { Step } from '../types';

/**
 * Maps a backend step string to the frontend Step enum
 */
export const mapBackendStepToEnum = (step: string): Step => {
  return step === 'selection' ? Step.SELECTION :
         step === 'data_capture' ? Step.DATA_CAPTURE :
         step === 'check' ? Step.COMPLIANCE :
         step === 'error' ? Step.DATA_CAPTURE :
         step === 'payment' ? Step.PAYMENT :
         step === 'thank_you' ? Step.THANK_YOU :
         Step.SELECTION;
};
