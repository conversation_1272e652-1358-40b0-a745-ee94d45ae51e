<?php

/**
 * @file
 * Token integration for the price calculator.
 */

/**
 * Implements hook_token_info().
 */
function price_calculator_token_info() {
  $info['tokens']['site']['price-calculator'] = [
    'name' => t('Price Calculator'),
    'description' => t('Embeds the price calculator component.'),
  ];

  return $info;
}

/**
 * Implements hook_tokens().
 */
function price_calculator_tokens($type, $tokens, array $data, array $options, \Drupal\Core\Render\BubbleableMetadata $bubbleable_metadata) {
  $replacements = [];

  if ($type == 'site' && in_array('price-calculator', array_keys($tokens))) {
    $container_id = 'price-calculator-' . uniqid();
    $replacements[$tokens['price-calculator']] = [
      '#theme' => 'price_calculator',
      '#container_id' => $container_id,
      '#attached' => [
        'library' => ['price_calculator/calculator'],
      ],
    ];
  }

  return $replacements;
} 