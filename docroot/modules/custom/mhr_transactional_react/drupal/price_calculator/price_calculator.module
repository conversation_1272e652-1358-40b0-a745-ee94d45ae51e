<?php

/**
 * @file
 * Primary module hooks for Price Calculator module.
 */

/**
 * Implements hook_theme().
 */
function price_calculator_theme($existing, $type, $theme, $path) {
  return [
    'price_calculator' => [
      'variables' => [
        'container_id' => NULL,
      ],
      'template' => 'price-calculator',
    ],
  ];
}

/**
 * Implements hook_page_attachments().
 */
function price_calculator_page_attachments(array &$attachments) {
  $attachments['#attached']['library'][] = 'price_calculator/calculator';
}  