<?php


namespace Drupal\mhr_customizations\Plugin\Block;

use <PERSON><PERSON>al\Core\Access\AccessResult;
use <PERSON>upal\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Session\AccountInterface;

/**
 * Provides a block with a simple text.
 *
 * @Block(
 *   id = "domain_selector_block",
 *   admin_label = @Translation("Domain Selector"),
 * )
 */
class DomainSelector extends BlockBase
{
  /**
   * {@inheritdoc}
   */
  public function build()
  {

    // Load all domains
    $storage = \Drupal::entityTypeManager()->getStorage('domain');
    $domains = $storage->loadMultiple();

    // get the active domain
    $current_domain = \Drupal::service('domain.negotiator')->getActiveDomain();
    $domain_suffix = $current_domain->getThirdPartySetting('country_path', 'domain_path');

    $markup = "<ul>";

    foreach ($domains as $domain){
      //Removed US from domain selector, add again remove the extra condition checking $domain->id()
      if ($domain->label() !== 'Region Selector') {
        $domain_country_code = $domain->getThirdPartySetting('country_path', 'domain_path');
        if ($domain->status()) {
          $link_class = $domain_country_code;
          if ($domain_suffix == $domain_country_code) {
            $link_class .= ' active';
          }
          $lang = \Drupal::languageManager()->getCurrentLanguage();
          $langcode = '';
          if (isset($lang)) {
            $langcode = '/'.$lang->getId();
          }
          // Set the domain switcher labels
          $domain_label = "";
          if ($domain->get('name') == "UK") {
            $domain_label = "UK & IE";
          }
          elseif ($domain->get('name') == "US")
            $domain_label = "US";

          $markup .= '<li class="' . $link_class . '"><a href="' . $domain->getPath() . $domain_country_code . $langcode . '"><span>' . $domain_label . '</span></a></li>';
        }
        else {
          if ($domain_country_code === 'uk') {
            // UK disabled - so make UK link fo to the old mhr.co.uk site
            $markup .= '<li class="uk"><a href="https://www.mhr.co.uk"><span>UK &nearr;</span></a></li>';
          }
        }
      }
    }

    $markup.="</ul>";

    return [
      '#markup' => $markup,
      '#cache' => [
        'max-age' => 0,
      ]
    ];
  }

  /**
   * {@inheritdoc}
   */
  protected function blockAccess(AccountInterface $account)
  {
    return AccessResult::allowedIfHasPermission($account, 'access content');
  }

  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state)
  {
    $config = $this->getConfiguration();

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state)
  {
    $this->configuration['my_block_settings'] = $form_state->getValue('my_block_settings');
  }

  public function getCacheMaxAge() {
    return 0;
  }
}
