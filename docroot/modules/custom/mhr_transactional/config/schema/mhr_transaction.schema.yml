mhr_transactional.calculator.settings:
  type: config_object
  label: 'Calculator settings'
  mapping:
    packages:
      type: mapping
      label: 'Package pricing'
      mapping:
        hr_only:
          type: mapping
          label: 'HR only package'
          mapping:
            name:
              type: string
              label: 'Package name'
            price:
              type: float
              label: 'Price per employee'
        hr_payroll:
          type: mapping
          label: 'HR & Payroll package'
          mapping:
            name:
              type: string
              label: 'Package name'
            price:
              type: float
              label: 'Price per employee'
    addons:
      type: mapping
      label: 'Add-on pricing'
      mapping:
        recruitment:
          type: mapping
          label: 'Recruitment add-on'
          mapping:
            name:
              type: string
              label: 'Add-on name'
            price:
              type: float
              label: 'Price per employee'
    quickStart:
      type: mapping
      label: 'Quick Start pricing'
      mapping:
        hr_only:
          type: float
          label: 'HR only Quick Start price'
        hr_payroll:
          type: float
          label: 'HR & Payroll Quick Start price'
mhr_transactional.stripe.settings:
  type: config_object
  label: 'MHR Transactional Stripe settings'
  mapping:
    test_mode:
      type: boolean
      label: 'Test mode'
    publishable_key_test:
      type: string
      label: 'Test publishable key'
    secret_key_test:
      type: string
      label: 'Test secret key'
    publishable_key_live:
      type: string
      label: 'Live publishable key'
    secret_key_live:
      type: string
      label: 'Live secret key'
    currency:
      type: string
      label: 'Default currency'
