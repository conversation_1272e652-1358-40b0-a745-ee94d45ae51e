<?php

namespace Drupal\mhr_transactional\EventSubscriber;

use <PERSON>upal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\mhr_transactional\MHRTransactionalConstants;
use Drupal\salesforce\Event\SalesforceEvents;
use Drupal\salesforce_mapping\Event\SalesforcePushAllowedEvent;
use Drupal\salesforce_mapping\Event\SalesforcePushOpEvent;
use Drupal\salesforce_mapping\Event\SalesforcePushParamsEvent;
use Drupal\salesforce_mapping\MappingConstants;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Event subscriber for controlling MHRCommerceOrder Salesforce pushes.
 *
 * This subscriber:
 * - Prevents Salesforce pushes for MHRCommerceOrder entities
 *   unless they are in specific "step" statuses.
 * - Captures and logs detailed error information when a push to
 *   Salesforce fails for an MHRCommerceOrder entity.
 * - Overrides the operation type to 'create' for existing MHRCommerceOrder entities
 *   that don't yet exist in Salesforce.
 */
class MHRCommerceSalesforcePushSubscriber implements EventSubscriberInterface {

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Constructor.
   *
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory service.
   */
  public function __construct(LoggerChannelFactoryInterface $logger_factory) {
    $this->loggerFactory = $logger_factory;
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    return [
      SalesforceEvents::PUSH_ALLOWED => ['onPushAllowed', 100],
      SalesforceEvents::PUSH_PARAMS => ['onPushParams', 100],
      SalesforceEvents::PUSH_FAIL => ['onPushFail', 100],
      SalesforceEvents::PUSH_SUCCESS => ['onPushSuccess', 100]
    ];
  }

  /**
   * Check if the MHRCommerceOrder entity should be pushed to Salesforce.
   *
   * @param \Drupal\salesforce_mapping\Event\SalesforcePushAllowedEvent $event
   *   The push allowed event.
   */
  public function onPushAllowed(SalesforcePushAllowedEvent $event) {
    $entity = $event->getEntity();

    // Only process MHRCommerceOrder entities
    if ($entity->getEntityTypeId() !== 'mhr_commerce_order') {
      return;
    }

    // Get the current step value
    $step = $entity->get('step')->value;

    // Define the steps that should trigger a Salesforce push
    $allowed_steps = [
      MHRTransactionalConstants::COMPLIANCE,
      MHRTransactionalConstants::CHECK,
      MHRTransactionalConstants::COMPLETE
    ];

    // If the step is not in the allowed list, prevent the push
    if (!in_array($step, $allowed_steps)) {
      $this->loggerFactory->get('mhr_transactional')->debug('Preventing Salesforce push for MHRCommerceOrder @id with step @step', [
        '@id' => $entity->id(),
        '@step' => $step,
      ]);
      $event->disallowPush();
    }
    else {
      $this->loggerFactory->get('mhr_transactional')->debug('Allowing Salesforce push for MHRCommerceOrder @id with step @step', [
        '@id' => $entity->id(),
        '@step' => $step,
      ]);
    }
  }


  /**
   * Log details when a Salesforce push operation fails.
   *
   * @param \Drupal\salesforce_mapping\Event\SalesforcePushOpEvent $event
   *   The push fail event.
   */
  public function onPushFail(SalesforcePushOpEvent $event) {
    $mapping_object = $event->getMapping();
    if (!$mapping_object) {
      return;
    }

    $entity = $event->getEntity();
    if (!$entity || $entity->getEntityTypeId() !== 'mhr_commerce_order') {
      return;
    }
    // Log detailed error information
    $this->loggerFactory->get('mhr_transactional')->error('Salesforce push failed for MHRCommerceOrder @id', [
      '@id' => $entity->id(),
    ]);
  }

  /**
   * Logs when a entity is mapped successfully to salesforce.
   *
   * @param \Drupal\salesforce_mapping\Event\SalesforcePushParamsEvent $event
   *   Push params event with salesforce ID.
   */
  public function onPushSuccess(SalesforcePushParamsEvent $event) {

    $entity = $event->getEntity();
    if (!$entity || $entity->getEntityTypeId() !== 'mhr_commerce_order') {
      return;
    }
    $this->loggerFactory->get('mhr_transactional')->info('Salesforce push OK for MHRCommerceOrder @id - sfid: @sfid', [
      '@id' => $entity->id(),
      '@sfid' => $event->getMappedObject()->sfid()
    ]);

  }

  /**
   * If the step is not "complete" or "check" then check whether the order is
   * complete, and if not, assume we are trying to make a "check" call
   *
   * @param \Drupal\salesforce_mapping\Event\SalesforcePushParamsEvent $event
   *
   * @return void
   */
  public function onPushParams(SalesforcePushParamsEvent $event) {
    $entity = $event->getEntity();
    if (!$entity || $entity->getEntityTypeId() !== 'mhr_commerce_order') {
      return;
    }

    $step = $entity->get('step')->value;
    if ($step !== MHRTransactionalConstants::COMPLETE
      && $step !== MHRTransactionalConstants::CHECK) {
      $params = $event->getParams();
      if ($step == MHRTransactionalConstants::THANK_YOU) {
        $newstep = MHRTransactionalConstants::COMPLETE;
      } else {
        $newstep = MHRTransactionalConstants::CHECK;
      }
      $params->setParam('Step__c', $newstep);
      $this->loggerFactory->get('mhr_transactional')->debug('Salesforce step changed from @step to @newstep', [
        '@step' => $step,
        '@newstep' => $newstep
      ]);
    }
  }
}
