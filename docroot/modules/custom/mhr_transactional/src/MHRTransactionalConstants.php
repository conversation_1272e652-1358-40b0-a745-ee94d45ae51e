<?php

namespace Drupal\mhr_transactional;

final class MHRTransactionalConstants {
  const SELECTION = "selection"; // product selector
  const DATA_CAPTURE = "data_capture"; // contact details
  const COMPLIANCE = "compliance"; // compliance / validity check
  const CHECK = "check"; // check - old 'compliance' step and what SF is expecting. Only used as a transitory step to send to SF
  const PAYMENT = "payment"; // payment
  const COMPLETE = "complete"; // paid
  const PAID = "paid"; // paid
  const THANK_YOU = "thank_you"; // paid
  const CONFIRMED = "confirmed"; // paid and confirmed by Salesforce
  const ERROR = "error"; // error returned by Salesforce
}
