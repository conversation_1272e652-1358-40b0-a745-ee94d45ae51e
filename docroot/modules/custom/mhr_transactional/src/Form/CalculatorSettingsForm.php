<?php

namespace Drupal\mhr_transactional\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * Configuration form for calculator pricing settings.
 */
class CalculatorSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['mhr_transactional.calculator.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mhr_transactional_calculator_settings';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('mhr_transactional.calculator.settings');

    // Add cache tags to ensure form reflects latest configuration
    $form['#cache'] = [
      'tags' => ['config:mhr_transactional.calculator.settings'],
    ];

    // Package pricing section
    $form['packages'] = [
      '#type' => 'details',
      '#title' => $this->t('Package pricing'),
      '#open' => TRUE,
      '#tree' => TRUE,
    ];

    // HR Only package
    $form['packages']['hr_only'] = [
      '#type' => 'details',
      '#title' => $this->t('HR only package'),
      '#open' => TRUE,
    ];

    $form['packages']['hr_only']['name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Name'),
      '#default_value' => $config->get('packages.hr_only.name') ?? 'HR Only',
      '#required' => TRUE,
      '#name' => 'packages[hr_only][name]',
    ];

    $form['packages']['hr_only']['price'] = [
      '#type' => 'number',
      '#title' => $this->t('Price per employee'),
      '#default_value' => $config->get('packages.hr_only.price') ?? 3.50,
      '#step' => 0.01,
      '#required' => TRUE,
      '#name' => 'packages[hr_only][price]',
    ];

    // HR & Payroll package
    $form['packages']['hr_payroll'] = [
      '#type' => 'details',
      '#title' => $this->t('HR & Payroll package'),
      '#open' => TRUE,
    ];

    $form['packages']['hr_payroll']['name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Name'),
      '#default_value' => $config->get('packages.hr_payroll.name') ?? 'HR & Payroll',
      '#required' => TRUE,
      '#name' => 'packages[hr_payroll][name]',
    ];

    $form['packages']['hr_payroll']['price'] = [
      '#type' => 'number',
      '#title' => $this->t('Price per employee'),
      '#default_value' => $config->get('packages.hr_payroll.price') ?? 6.00,
      '#step' => 0.01,
      '#required' => TRUE,
      '#name' => 'packages[hr_payroll][price]',
    ];

    // Add-ons section
    $form['addons'] = [
      '#type' => 'details',
      '#title' => $this->t('Add-on pricing'),
      '#open' => TRUE,
      '#tree' => TRUE,
    ];

    $addons = [
      'recruitment' => 'Recruitment',
    ];

    foreach ($addons as $key => $label) {
      $form['addons'][$key] = [
        '#type' => 'details',
        '#title' => $this->t('@label add-on', ['@label' => $label]),
        '#open' => TRUE,
      ];

      $form['addons'][$key]['name'] = [
        '#type' => 'textfield',
        '#title' => $this->t('Name'),
        '#default_value' => $config->get("addons.$key.name") ?? $label,
        '#required' => TRUE,
        '#parents' => ['addons', $key, 'name'],
      ];

      $form['addons'][$key]['price'] = [
        '#type' => 'number',
        '#title' => $this->t('Price per employee'),
        '#default_value' => $config->get("addons.$key.price") ?? ($key === 'recruitment' ? 0.85 : ($key === 'onboarding' ? 0.50 : 0.60)),
        '#step' => 0.01,
        '#required' => TRUE,
        '#parents' => ['addons', $key, 'price'],
      ];
    }

    // Quick Start section
    $form['quickStart'] = [
      '#type' => 'details',
      '#title' => $this->t('Quick Start pricing'),
      '#open' => TRUE,
      '#tree' => TRUE,
    ];

    $form['quickStart']['hr_only'] = [
      '#type' => 'number',
      '#title' => $this->t('HR only Quick Start price'),
      '#default_value' => $config->get('quickStart.hr_only') ?? 2240,
      '#step' => 0.01,
      '#required' => TRUE,
      '#parents' => ['quickStart', 'hr_only'],
    ];

    $form['quickStart']['hr_payroll'] = [
      '#type' => 'number',
      '#title' => $this->t('HR & Payroll Quick Start price'),
      '#default_value' => $config->get('quickStart.hr_payroll') ?? 4340,
      '#step' => 0.01,
      '#required' => TRUE,
      '#parents' => ['quickStart', 'hr_payroll'],
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('mhr_transactional.calculator.settings');

    // Save package settings
    foreach (['hr_only', 'hr_payroll'] as $package) {
      $name = $form_state->getValue(['packages', $package, 'name']);
      $price = $form_state->getValue(['packages', $package, 'price']);

      if ($name !== NULL) {
        $config->set("packages.$package.name", $name);
      }
      if ($price !== NULL) {
        $config->set("packages.$package.price", (float) $price);
      }
    }

    // Save add-on settings
    foreach (['recruitment'] as $addon) {
      $name = $form_state->getValue(['addons', $addon, 'name']);
      $price = $form_state->getValue(['addons', $addon, 'price']);

      if ($name !== NULL) {
        $config->set("addons.$addon.name", $name);
      }
      if ($price !== NULL) {
        $config->set("addons.$addon.price", (float) $price);
      }
    }

    // Save Quick Start settings
    $hr_only = $form_state->getValue(['quickStart', 'hr_only']);
    $hr_payroll = $form_state->getValue(['quickStart', 'hr_payroll']);

    if ($hr_only !== NULL) {
      $config->set('quickStart.hr_only', (float) $hr_only);
    }
    if ($hr_payroll !== NULL) {
      $config->set('quickStart.hr_payroll', (float) $hr_payroll);
    }

    $config->save();

    // Clear render cache to ensure React app gets updated config
    \Drupal::service('cache_tags.invalidator')->invalidateTags(['config:mhr_transactional.calculator.settings']);

    parent::submitForm($form, $form_state);
  }
}
