<?php

namespace Drupal\mhr_transactional\Service;

use Drupal\Core\Config\ConfigFactoryInterface;

/**
 * Service for processing payments through Stripe.
 */
class PaymentProcessor {

  /**
   * The config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Constructs a PaymentProcessor object.
   *
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The config factory.
   */
  public function __construct(ConfigFactoryInterface $config_factory) {
    $this->configFactory = $config_factory;
  }

  /**
   * Process a payment.
   *
   * @param array $payment_data
   *   The payment data.
   *
   * @return array
   *   The payment result.
   */
  public function processPayment(array $payment_data) {
    try {
      $stripe = $this->initClient();

      // Get Stripe configuration from our module.
      $config = $this->configFactory->get('mhr_transactional.stripe.settings');

      // Create a checkout session.
      $checkout_session = $stripe->checkout->sessions->create([
        'payment_method_types' => ['card'],
        'line_items' => [[
          'price_data' => [
            'currency' => $payment_data['currency'] ?? $config->get('currency') ?? 'GBP',
            'product_data' => [
              'name' => $payment_data['product_name'] ?? 'MHR Subscription',
            ],
            'unit_amount' => $payment_data['amount'],
          ],
          'quantity' => $payment_data['quantity'] ?? 1,
        ]],
        'mode' => 'payment',
        'success_url' => $payment_data['success_url'] ?? \Drupal::request()->getSchemeAndHttpHost() . '/thank-you',
        'cancel_url' => $payment_data['cancel_url'] ?? \Drupal::request()->getSchemeAndHttpHost(),
      ]);

      return [
        'success' => TRUE,
        'id' => $checkout_session->id,
      ];
    }
    catch (\Exception $e) {
      return [
        'success' => FALSE,
        'error' => $e->getMessage(),
      ];
    }
  }

  /**
   * Confirm that the given checkout session id has been paid for
   *
   * @param $sessionId string stripe session id
   *
   * @return boolean true if the payment is complete, false otherwise
   */
  public function validatePayment($sessionId) {
    $stripe = $this->initClient();

    try {
      // Retrieve the checkout session
      $session = $stripe->checkout->sessions->retrieve($sessionId, []);

      // Check the payment status - note could also be "no_payment_required"
      // but for MHR they won't be expecting to have zero-price sales, so we will
      // assume that this is also an invalid payment
      if ($session->payment_status === 'paid') {
        // Get the transaction id
        $paymentIntentId = $session->payment_intent;

        if ($paymentIntentId) {
          // Retrieve the PaymentIntent object
          $paymentIntent = $stripe->paymentIntents->retrieve($paymentIntentId, []);

          $transactionId = $paymentIntent->latest_charge;

          if ($transactionId) {
            return $transactionId;
          }
        } else {
          \Drupal::logger('mhr_transactional')->debug('No payment intent found for session id @sessionid', [
            '@sessionid' => $sessionId
          ]);
        }

      } else {
        \Drupal::logger('mhr_transactional')->debug('Session id @sessionid has not been paid - payment status is @status', [
          '@sessionid' => $sessionId,
          '@status' => $session->payment_status
        ]);
      }
    } catch (\Stripe\Exception\ApiErrorException $e) {
      \Drupal::logger('mhr_transactional')->debug('Exception validating payment for @sessionid : @msg', [
        '@sessionid' => $sessionId,
        '@msg' => $e->getMessage()
      ]);
    }

    return FALSE;
  }

  private function initClient() {
      // Get Stripe configuration from our module.
      $config = $this->configFactory->get('mhr_transactional.stripe.settings');
      $test_mode = $config->get('test_mode');

      if ($test_mode) {
        $secret_key = $config->get('secret_key_test');
      } else {
        $secret_key = $config->get('secret_key_live');
      }

      if (empty($secret_key)) {
        throw new \Exception('Stripe API key is not configured.');
      }

      // Initialize Stripe client.
      return new \Stripe\StripeClient($secret_key);
  }
}
