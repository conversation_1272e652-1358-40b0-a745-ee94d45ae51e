services:
  mhr_transactional.calculator_submission:
    class: Drupal\mhr_transactional\Service\CalculatorSubmissionService
    arguments: ['@tempstore.private']

  mhr_transactional.email_validation:
    class: Drupal\mhr_transactional\Service\EmailValidationService
    arguments: ['@email.validator', '@config.factory']
  mhr_transactional.stripe.payment_processor:
    class: Drupal\mhr_transactional\Service\PaymentProcessor
    arguments: ['@config.factory']
  mhr_transactional.salesforce_push_subscriber:
    class: Drupal\mhr_transactional\EventSubscriber\MHRCommerceSalesforcePushSubscriber
    arguments: ['@logger.factory']
    tags:
      - { name: event_subscriber }
