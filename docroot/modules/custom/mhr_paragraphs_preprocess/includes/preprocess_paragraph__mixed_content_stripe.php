<?php

use \Drupal\Core\Url;
use \Drupal\node\Entity\Node;

/**
 * Implements hook_preprocess_paragraph()
 *
 * Build render array for carousel.
 *
 * Loads node render arrays for each card in the carousel, grouped by their
 * content type, which is used for filtering on the front end.
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mixed_content_stripe (&$variables) {

  $paragraph = $variables['paragraph'];

  // field_mhr_mcs_appearance
  $field_mhr_mcs_appearance = 'standardcarousel'; // Set a default choice.
  if (
    $paragraph->hasField('field_mhr_mcs_appearance') &&
    $paragraph->field_mhr_mcs_appearance->isEmpty() == FALSE
  ) {
    $field_mhr_mcs_appearance = $paragraph->field_mhr_mcs_appearance->value;
  }

  // field_title_h2
  $field_title_h2 = null;
  if (
    $paragraph->hasField('field_title_h2') &&
    $paragraph->field_title_h2->isEmpty() == FALSE
  ) {
    $field_title_h2 = $paragraph->field_title_h2->value;
  }
  $variables['mixed_content_stripe']['mixed_content_stripe_title'] = $field_title_h2;

  // field_mixed_content_tag
  // This field refers to terms from the Tags vocabulary. Here the relevant content to display
  // is worked out.
  $tags = [];
  $allNids = [];
  if (
    $paragraph->hasField('field_mixed_content_tag') &&
    $paragraph->field_mixed_content_tag->isEmpty() == FALSE
  ) {
    $tags = $paragraph->field_mixed_content_tag->getValue();
    $tags = array_column($tags, 'target_id');
  }
  $content_types = ['blog_post', 'download_listing_item', 'case_study'];
  foreach ($content_types as $content_type) {
    $qry = \Drupal::entityQuery('node')
      ->accessCheck(TRUE)
      ->condition('status', 1)
      ->condition('type', $content_type)
      ->sort('created', 'DESC')
      ->range(0, 9);
    if ($tags) {
      $orCondition = $qry->orConditionGroup();
      $orCondition->condition('field_tags', $tags, 'IN');
      $orCondition->condition('field_blog_tags', $tags, 'IN');
      //HELPDESK-6972 Also allow content for matches against the content hub topic field
      $orCondition->condition('field_content_hub_topic', $tags, 'IN');
      $qry->condition($orCondition);
    }
    $nids = $qry->execute();
    if ($nids) {
      $allNids = array_merge($allNids, $nids);
    }
  }
  // Run through the content we've found, and render a generic card
  // for each item. At the same time, build the chooser widget that is
  // run using mhr-structured-content-carousel.js.
  $entity_type = 'node';
  $mixed_content_stripe_cards = [];
  $mixed_content_stripe_chooser_options = ['mixed']; // Always include 'mixed' option

  $nodes = \Drupal::entityTypeManager()
    ->getStorage($entity_type)
    ->loadMultiple($allNids);

  // Generate cards with both mixed and type-specific classes
  foreach ($nodes as $node) {
    $content_type = $node->bundle();
    // Add both mixed and content-type specific classes
    $classes = [
      'mhr-mixed-content-mixed',
      'mhr-mixed-content-' . $content_type
    ];
    $node_card = _mhr_render_card_for_mixed_content_component_item($node, implode(' ', $classes));
    $mixed_content_stripe_cards[] = $node_card;

    // Add content type to chooser options if not already present
    if (!in_array($content_type, $mixed_content_stripe_chooser_options)) {
      $mixed_content_stripe_chooser_options[] = $content_type;
    }
  }

  // Set up carousel settings for Swiper, based on the 'Appearance' selection made.
  switch ($field_mhr_mcs_appearance) {
    case 'enhancedcarousel':
      $carousel_settings = _mhr_enhanced_carousel_swiper_settings();
      break;
    case 'standardcarousel':
    default:
      $carousel_settings = json_encode([
        'slidesPerGroup' => 1,
        'slidesPerView' => 1,
        'spaceBetween' => 24,
        'speed' => 250,
        'watchSlidesProgress' => true,
        'breakpoints' => [
          '768' => [
            'slidesPerGroup' => 2,
            'slidesPerView' => 2,
          ],
          '992' => [
            'slidesPerGroup' => 2,
            'slidesPerView' => 2,
          ],
          '1200' => [
            'slidesPerGroup' => 3,
            'slidesPerView' => 3,
          ],
        ],
      ]);
      break;
  }
  $mixed_content_carousel_render_array = [
    '#theme' => 'vsc_carousel_grid',
    '#carousel_items' => $mixed_content_stripe_cards,
    '#carouselSettings' => $carousel_settings,
    '#vsc_carousel_prev_icon' => 'arrow-right',
    '#vsc_carousel_next_icon' => 'arrow-right',
  ];
  $mixed_content_carousel_render_array['#attached']['library'][] = 'mhr/mhr-carousel';
  $variables['mixed_content_stripe']['mixed_content_stripe_carousel'] = $mixed_content_carousel_render_array;
  // Build chooser markup.
  $chooser_options_markup = '';
  foreach(array_unique($mixed_content_stripe_chooser_options) as $mixed_content_stripe_chooser_options_single) {
    switch($mixed_content_stripe_chooser_options_single) {
      case 'mixed':
        $chooser_option_label = 'All';
        break;
      case 'blog_post':
        $chooser_option_label = 'Blog';
        break;
      case 'download_listing_item':
        $chooser_option_label = 'Download';
        break;
      case 'case_study':
        $chooser_option_label = 'Case Study';
        break;
      default:
        $chooser_option_label = $mixed_content_stripe_chooser_options_single;
        break;
    }
    $chooser_options_markup .= '<option value="' . $mixed_content_stripe_chooser_options_single . '">' . $chooser_option_label . '</option>';
  }
  $chooser_markup = '<select name="mixed-content-filter" class="mixed-content-filter" aria-label="Filter">' . $chooser_options_markup . '</select>';
  $variables['mixed_content_stripe']['mixed_content_stripe_chooser'] = [
    '#type' => 'inline_template',
    '#template' => $chooser_markup,
  ];
}

/**
 * Helper function to generate a generic card for an item in the mixed content
 * component.
 *
 * @param Node $node
 *   The node to generate a card for.
 *
 * @param string $wrapper_class
 *   CSS class to add to card wrapper element.
 *
 * @return mixed
 *   Render array for the card if generated, or null.
 */
function _mhr_render_card_for_mixed_content_component_item(Node $node = NULL, string $wrapper_class = NULL) {
  if (
    !isset($node) ||
    !isset($wrapper_class)
  ) {
    return null;
  }
  switch($node->bundle()) {
    case 'blog_post':
      $card_body = $node->field_intro->value;
      $card_heading = $node->label();
      $card_image = null;
      $field_image_data = _mhr_get_image_data($node->field_image);
      if ($field_image_data) {
        $card_image = [
          '#height' => $field_image_data['image_height'],
          '#responsive_image_style_id' => 'blog_teaser',
          '#theme' => 'responsive_image',
          '#uri' => $field_image_data['image_uri'],
          '#width' => $field_image_data['image_width'],
        ];
      }
      $card_link = Url::fromRoute('entity.node.canonical', ['node' => $node->id()], ['absolute' => true])->toString();
      break;
    case 'download_listing_item':
      $card_body = $node->body->view([
        'label' => 'hidden',
        'type' => 'smart_trim',
        'settings' => [
          'summary_handler' => 'trim',
          'trim_length' => 30,
          'trim_suffix' => '…',
          'trim_type' => 'words',
          'trim_options' => [
            'text' => true,
          ],
        ],
      ]);
      $card_heading = $node->field_heading->value;
      $card_image = null;
      $field_image_data = _mhr_get_image_data($node->field_resource_image);
      if ($field_image_data) {
        $card_image = [
          '#height' => $field_image_data['image_height'],
          '#responsive_image_style_id' => 'grid',
          '#theme' => 'responsive_image',
          '#uri' => $field_image_data['image_uri'],
          '#width' => $field_image_data['image_width'],
        ];
      }
      $card_link = Url::fromUri($node->field_button->getValue()[0]['uri']);
      break;
    case 'case_study':
      $card_body = $node->body->view([
        'label' => 'hidden',
        'type' => 'smart_trim',
        'settings' => [
          'summary_handler' => 'trim',
          'trim_length' => 30,
          'trim_suffix' => '…',
          'trim_type' => 'words',
          'trim_options' => [
            'text' => true,
          ],
        ],
      ]);
      $card_heading = $node->field_heading->value;
      $card_image = null;
      $field_image_data = _mhr_get_image_data($node->field_resource_image);
      if ($field_image_data) {
        $card_image = [
          '#height' => $field_image_data['image_height'],
          '#responsive_image_style_id' => 'grid',
          '#theme' => 'responsive_image',
          '#uri' => $field_image_data['image_uri'],
          '#width' => $field_image_data['image_width'],
        ];
      }
      $card_link = Url::fromUri($node->field_button->getValue()[0]['uri']);
      break;
  }
  $node_card = twig_render_template(
    \Drupal::service('extension.list.theme')->getPath('mhr') . '/templates/mhr-card.html.twig',
    [
      'card' => [
        'card_body' => $card_body,
        'card_custom_wrapper_classes_array' => [$wrapper_class],
        'card_heading' => $card_heading,
        'card_image' => $card_image,
        'card_link' => [
          'url' => $card_link,
          'label' => $card_heading,
        ],
      ],
      'theme_hook_original' => 'not-applicable',
      'directory' => '',
    ]
  );
  return $node_card;
}
