<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_heading_text_icon_cta (&$variables) {

  $paragraph = $variables['paragraph'];

  $paragraph_data = [];

  $paragraph_data['mhr_heading_text_icon_cta_heading'] = _mhr_render_mhr_heading_with_icon_field($paragraph, 'field_mhr_hticta_heading');

  // field_mhr_hticta_text
  $field_mhr_hticta_text = null;
  if (
    $paragraph->hasField('field_mhr_hticta_text') &&
    $paragraph->field_mhr_hticta_text->isEmpty() == FALSE
  ) {
    $field_mhr_hticta_text = $paragraph->field_mhr_hticta_text->value;
  }
  $paragraph_data['mhr_heading_text_icon_cta_text'] = $field_mhr_hticta_text;

  // field_cta_button_universal
  $field_cta_button_universal = null;
  if (
    $paragraph->hasField('field_cta_button_universal') &&
    $paragraph->field_cta_button_universal->isEmpty() == FALSE
  ) {
    $field_cta_button_universal = $paragraph->field_cta_button_universal->view([
      'label' => 'hidden',
    ]);
  }
  $paragraph_data['mhr_heading_text_icon_cta_button'] = $field_cta_button_universal;

  $variables['mhr_heading_text_icon_cta'] = $paragraph_data;
}
