<?php

/**
 * Implements hook_preprocess_paragraph_HOOK().
 *
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__carousel(&$variables) {

  // Note, this Paragraph's machine name is 'carousel' but it appears to be used for
  // showing testimonials in a slider/carousel format.

  $variables['#attached']['library'][] = 'mhr/mhr-carousel';

  $paragraph = $variables['paragraph'];

  // field_heading
  $field_heading = null;
  if (
    $paragraph->hasField('field_heading') &&
    $paragraph->field_heading->isEmpty() == FALSE
  ) {
    $field_heading = $paragraph->field_heading->value;
  }

  // field_slide (contains the multiple testimonials)
  $testimonial_slides = [];
  $field_slide = null;
  if (
    $paragraph->hasField('field_slide') &&
    $paragraph->field_slide->isEmpty() == FALSE
  ) {
    // $field_slide contains references to 'Testimonial Slide' paragraph entities.
    // So here we gather up the necessary data from those entities to pass to the
    // template.
    $testimonial_entities = $paragraph->field_slide->referencedEntities();
    foreach($testimonial_entities as $testimonial_entities_single) {
      // Within each 'Testimonial Slide' paragraph entity is the field_carousel_content
      // field which contains references to 'testimonial' content type nodes.
      if (
        $testimonial_entities_single->hasField('field_carousel_content') &&
        $testimonial_entities_single->field_carousel_content->isEmpty() == FALSE
      ) {
        $field_carousel_content = $testimonial_entities_single->field_carousel_content->referencedEntities();
        foreach($field_carousel_content as $field_carousel_content_single) {
          // Company name
          $field_organisation = null;
          if (
            $field_carousel_content_single->hasField('field_organisation') &&
            $field_carousel_content_single->field_organisation->isEmpty() == FALSE
          ) {
            $field_organisation = $field_carousel_content_single->field_organisation->value;
          }
          // Logo
          $field_logo = null;
          if (
            $field_carousel_content_single->hasField('field_logo') &&
            $field_carousel_content_single->field_logo->isEmpty() == FALSE
          ) {
            $field_logo_image_data = _mhr_get_image_data($field_carousel_content_single->field_logo);
            if ($field_logo_image_data) {
              $field_logo = [
                '#height' => $field_logo_image_data['image_height'],
                '#responsive_image_style_id' => 'testimonial_slider',
                '#theme' => 'responsive_image',
                '#uri' => $field_logo_image_data['image_uri'],
                '#width' => $field_logo_image_data['image_width'],
              ];
            }
          }
          // Testimonial
          $field_testimonial = null;
          if (
            $field_carousel_content_single->hasField('field_testimonial') &&
            $field_carousel_content_single->field_testimonial->isEmpty() == FALSE
          ) {
            $field_testimonial = $field_carousel_content_single->field_testimonial->value;
          }
          // Button
          $field_testimonial_cta = null;
          if (
            $field_carousel_content_single->hasField('field_testimonial_cta') &&
            $field_carousel_content_single->field_testimonial_cta->isEmpty() == FALSE
          ) {
            $field_testimonial_cta_data = $field_carousel_content_single->field_testimonial_cta->getValue()[0];
            $field_testimonial_cta = [
              'title' => isset($field_testimonial_cta_data['title']) ? $field_testimonial_cta_data['title'] : null,
              'url' => isset($field_testimonial_cta_data['uri']) ? $field_testimonial_cta_data['uri'] : null,
            ];
          }
          // Render item.
          // See https://www.jeffgeerling.com/blog/2019/rendering-twig-templates-programmatically-drupal-8
          // for details of twig_render_template.
          $testimonial_slides[] = twig_render_template(
            \Drupal::service('extension.list.module')->getPath('mhr_paragraphs_preprocess') . '/templates/paragraph--testimonial-slider-item.html.twig',
            [
              'mhr_testimonial_button' => $field_testimonial_cta,
              'mhr_testimonial_logo' => $field_logo,
              'mhr_testimonial_organisation' => $field_organisation,
              'mhr_testimonial_text' => $field_testimonial,
              'theme_hook_original' => 'not-applicable',
              'directory' => '',
            ]
          );
        }
      }
    }
  }

  $variables['carousel_items'] = $testimonial_slides;
  $variables['carousel_title'] = $field_heading;
  $variables['set_as_grid_layout'] = false;
  $variables['vsc_carousel_prev_icon'] = 'arrow-right';
  $variables['vsc_carousel_next_icon'] = 'arrow-right';

  // Set up carousel settings for Swiper.
  $variables['carouselSettings'] = json_encode([
    'slidesPerGroup' => 1,
    'slidesPerView' => 1,
    'spaceBetween' => 48,
    'speed' => 250,
    'watchSlidesProgress' => true,
    'breakpoints' => [
      '768' => [
        'slidesPerGroup' => 1,
        'slidesPerView' => 1,
      ],
      '992' => [
        'slidesPerGroup' => 1,
        'slidesPerView' => 1,
      ],
      '1200' => [
        'slidesPerGroup' => 1,
        'slidesPerView' => 1,
      ],
    ],
  ]);
}
