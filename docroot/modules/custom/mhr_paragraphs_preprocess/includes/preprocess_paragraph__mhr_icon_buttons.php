<?php

use \Drupal\Core\Url;

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_icon_buttons (&$variables) {

  $paragraph = $variables['paragraph'];

  // field_heading
  $field_heading = null;
  if (
    $paragraph->hasField('field_heading') &&
    $paragraph->field_heading->isEmpty() == FALSE
  ) {
    $field_heading = $paragraph->field_heading->value;
  }

  // field_icon_buttons_subheading
  $field_icon_buttons_subheading = null;
  if (
    $paragraph->hasField('field_icon_buttons_subheading') &&
    $paragraph->field_icon_buttons_subheading->isEmpty() == FALSE
  ) {
    $field_icon_buttons_subheading = $paragraph->field_icon_buttons_subheading->value;
  }

  // field_button_item
  // field_button_item is entity reference revisions to the Icon Button paragraph type.
  // Here we gather the relevant data to pass to the template.
  $field_button_item = [];
  if (
    $paragraph->hasField('field_button_item') &&
    $paragraph->field_button_item->isEmpty() == FALSE
  ) {
    $field_button_item_objects = $paragraph->field_button_item->referencedEntities();
  }
  if (!empty($field_button_item_objects)) {
    foreach($field_button_item_objects as $field_button_item_data_single) {

      // field_icon_image
      $field_icon_image = null;
      if (
        $field_button_item_data_single->hasField('field_icon_image') &&
        $field_button_item_data_single->field_icon_image->isEmpty() == FALSE
      ) {
        $field_icon_image_data = _mhr_get_image_data($field_button_item_data_single->field_icon_image);
        if ($field_icon_image_data) {
          $field_icon_image = [
            '#height' => $field_icon_image_data['image_height'],
            '#responsive_image_style_id' => 'icon_buttons',
            '#theme' => 'responsive_image',
            '#uri' => $field_icon_image_data['image_uri'],
            '#width' => $field_icon_image_data['image_width'],
          ];
        }
      }

      // field_title_and_link
      $icon_type = 'Askusaquestion';
      $icon_link_title = 'More';
      $icon_link_url = null;
      if (
        $field_button_item_data_single->hasField('field_title_and_link') &&
        $field_button_item_data_single->field_title_and_link->isEmpty() == FALSE
      ) {
        $field_title_and_link = $field_button_item_data_single->field_title_and_link->getValue();
        // Assuming the link field is a standard Drupal link field
        $icon_link_title = $field_title_and_link[0]['title'];
        $icon_link_url = Url::fromUri($field_title_and_link[0]['uri']);
        if ($icon_link_title) {
          $link_title_text = strtolower($icon_link_title);
          // Check for specific words and assign button_color accordingly
          if (str_contains($link_title_text, 'download')) {
            $icon_type = 'Downloadbrochure';
          } elseif (str_contains($link_title_text, 'call')) {
            $icon_type = 'Scheduleacall';
          } elseif (str_contains($link_title_text, 'demo')) {
            $icon_type = 'Bookyourdemo';
          } elseif (str_contains($link_title_text, 'buy')) {
            $icon_type = 'Buynow';
          } elseif (str_contains($link_title_text, 'quote')) {
            $icon_type = 'Buynow';
          } elseif (str_contains($link_title_text, 'question')) {
            $icon_type = 'Askusaquestion';
          }
        }
      }

      // field_accordion_title
      $field_accordion_title = null;
      if (
        $field_button_item_data_single->hasField('field_accordion_title') &&
        $field_button_item_data_single->field_accordion_title->isEmpty() == FALSE
      ) {
        $field_accordion_title = $field_button_item_data_single->field_accordion_title->value;
      }

      // Prepare variables for this item.
      $field_button_item[] = [
        'icon_image' => $field_icon_image,
        'icon_link_title' => $icon_link_title,
        'icon_link_url' => $icon_link_url,
        'icon_title' => $field_accordion_title,
        'icon_type' => $icon_type,
      ];
    }
  }

  $variables['icon_buttons_output'] = [
    'icon_buttons_output_buttons' => $field_button_item,
    'icon_buttons_output_subheading' => $field_icon_buttons_subheading,
    'icon_buttons_output_title' => $field_heading,
  ];
}
