<?php

use Dr<PERSON>al\Core\Url;

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_testimonial_image_slider(&$variables) {

  $paragraph = $variables['paragraph'];

  $paragraph_data = [];

  // field_mhr_tis_appearance
  $field_mhr_tis_appearance = 'standard'; // Set a default choice.
  if (
    $paragraph->hasField('field_mhr_tis_appearance') &&
    $paragraph->field_mhr_tis_appearance->isEmpty() == FALSE
  ) {
    $field_mhr_tis_appearance = $paragraph->field_mhr_tis_appearance->value;
  }
  $paragraph_data['mhr_testimonial_image_slider_appearance'] = $field_mhr_tis_appearance;

  // Set up variable to hold the organisation names used in slides, to pass to the template for output.
  $organisation_names = null;

  // field_mhr_tis_slides (contains references to individual slides to be output)
  $field_mhr_tis_slides = null;
  if (
    $paragraph->hasField('field_mhr_tis_slides') &&
    $paragraph->field_mhr_tis_slides->isEmpty() == FALSE
  ) {
    foreach($paragraph->field_mhr_tis_slides->referencedEntities() as $single_tis_slide) {

      // field_mhr_tis_slides -> field_mhr_tis_background_image
      $field_mhr_tis_background_image = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_background_image') &&
        $single_tis_slide->field_mhr_tis_background_image->isEmpty() == FALSE
      ) {
        $field_mhr_tis_background_image_data = _mhr_get_image_data($single_tis_slide->field_mhr_tis_background_image);
        if ($field_mhr_tis_background_image_data) {
          $field_mhr_tis_background_image = [
            '#height' => $field_mhr_tis_background_image_data['image_height'],
            '#responsive_image_style_id' => 'hero_banner_gsap',
            '#theme' => 'responsive_image',
            '#uri' => $field_mhr_tis_background_image_data['image_uri'],
            '#width' => $field_mhr_tis_background_image_data['image_width'],
          ];
        }
      }

      // field_mhr_tis_slides -> field_mhr_tis_logo_image
      $field_mhr_tis_logo_image = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_logo_image') &&
        $single_tis_slide->field_mhr_tis_logo_image->isEmpty() == FALSE
      ) {
        $field_mhr_tis_logo_image_data = _mhr_get_image_data($single_tis_slide->field_mhr_tis_logo_image);
        if ($field_mhr_tis_logo_image_data) {
          $field_mhr_tis_logo_image = [
            '#height' => $field_mhr_tis_logo_image_data['image_height'],
            '#responsive_image_style_id' => 'testimonial_image_slider_logo',
            '#theme' => 'responsive_image',
            '#uri' => $field_mhr_tis_logo_image_data['image_uri'],
            '#width' => $field_mhr_tis_logo_image_data['image_width'],
          ];
        }
      }

      // field_mhr_tis_slides -> field_mhr_tis_testimonial_text
      $field_mhr_tis_testimonial_text = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_testimonial_text') &&
        $single_tis_slide->field_mhr_tis_testimonial_text->isEmpty() == FALSE
      ) {
        $field_mhr_tis_testimonial_text = $single_tis_slide->field_mhr_tis_testimonial_text->value;
      }

      // field_mhr_tis_slides -> field_mhr_tis_testimonial_source
      $field_mhr_tis_testimonial_source = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_testimonial_source') &&
        $single_tis_slide->field_mhr_tis_testimonial_source->isEmpty() == FALSE
      ) {
        $field_mhr_tis_testimonial_source = $single_tis_slide->field_mhr_tis_testimonial_source->value;
      }

      // field_mhr_tis_slides -> field_mhr_tis_testimonial_job
      $field_mhr_tis_testimonial_job = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_testimonial_job') &&
        $single_tis_slide->field_mhr_tis_testimonial_job->isEmpty() == FALSE
      ) {
        $field_mhr_tis_testimonial_job = $single_tis_slide->field_mhr_tis_testimonial_job->value;
      }

      // field_mhr_tis_slides -> field_mhr_tis_testimonial_org
      $field_mhr_tis_testimonial_org = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_testimonial_org') &&
        $single_tis_slide->field_mhr_tis_testimonial_org->isEmpty() == FALSE
      ) {
        $field_mhr_tis_testimonial_org = $single_tis_slide->field_mhr_tis_testimonial_org->value;
      }

      // field_mhr_tis_slides -> field_mhr_tis_link
      $field_mhr_tis_link = null;
      if (
        $single_tis_slide->hasField('field_mhr_tis_link') &&
        $single_tis_slide->field_mhr_tis_link->isEmpty() == FALSE
      ) {
        $field_mhr_tis_link = [];
        $field_mhr_tis_link_data = $single_tis_slide->field_mhr_tis_link->getValue()[0];
        if (isset($field_mhr_tis_link_data['uri'])) {
          $field_mhr_tis_link['url'] = Url::fromUri($field_mhr_tis_link_data['uri'])->toString(); // This should always work if a URI has been sent in the link field.
        }
        if (isset($field_mhr_tis_link_data['title'])) {
          $field_mhr_tis_link['text'] = $field_mhr_tis_link_data['title'];
        } else {
          $field_mhr_tis_link['text'] = $field_mhr_tis_link['url'];
        }
      }

      $field_mhr_tis_slides[] = [
        'background_image'   => $field_mhr_tis_background_image,
        'logo_image'         => $field_mhr_tis_logo_image,
        'testimonial_link'   => $field_mhr_tis_link,
        'testimonial_source_person_job_title' => $field_mhr_tis_testimonial_job,
        'testimonial_source_person_name' => $field_mhr_tis_testimonial_source,
        'testimonial_text'   => $field_mhr_tis_testimonial_text,
      ];

      $organisation_names[] = [
        'testimonial_source_organisation_name' => $field_mhr_tis_testimonial_org,
      ];
    }
  }

  $paragraph_data['mhr_testimonial_image_slider_slides'] = $field_mhr_tis_slides;

  $variables['mhr_testimonial_image_slider'] = $paragraph_data;

  // Get paragraph ID, so that the testimonial image slider organisation names worked out are
  // stored uniquely in the data passed to JavaScript. It is also added to the attributes array
  // sent to the template in order that each paragraph has a unique attribute that can be used by JavaScript.
  $paragraph_id = $paragraph->id();
  $variables['attributes']['data-mhr-testimonial-slider-id'] = $paragraph_id;
  $variables['#attached']['drupalSettings']['mhr']['organisationNames'][$paragraph_id] = $organisation_names;
}
