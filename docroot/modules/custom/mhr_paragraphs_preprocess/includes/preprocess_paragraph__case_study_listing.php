<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__case_study_listing (&$variables) {

  /** @var Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  $paragraph_variables = [];

  // field_title
  $paragraph_variables['title'] = null;
  if (
    $paragraph->hasField('field_title') &&
    $paragraph->field_title->isEmpty() === FALSE
  ) {
    $paragraph_variables['title'] = $paragraph->field_title->value;
  }

  // Get the output of the View that shows the case studies, and make it available as a variable
  // for this Paragraph type's template.
  $displayOptionArr = $paragraph->field_display_options->getValue();
  $displayOption = $displayOptionArr[0]["value"];
  $tags = $paragraph->field_tags->getValue();
  $tags = array_column($tags, 'target_id');
  $tags = implode(',', $tags);
  if ($tags === "") {
    $tags = null;
  }
  $related_industries = $paragraph->field_related_industry->getValue();
  $related_industries = array_column($related_industries, 'target_id');
  $related_industries = implode('+', $related_industries);
  if ($related_industries === "") {
    $related_industries = null;
  }
  if ($displayOption === "Latest 3") {
    $case_study_view_results = views_get_view_result('mhr_latest_case_studies', 'display_3_items', $related_industries);
  } elseif ($displayOption === "Latest 6") {
    $case_study_view_results = views_get_view_result('mhr_latest_case_studies', 'display_6_items', $related_industries);
  } elseif ($displayOption === "Latest 9") {
    $case_study_view_results = views_get_view_result('mhr_latest_case_studies', 'display_9_items', $related_industries);
  } else {
    $case_study_view_results = views_get_view_result('mhr_latest_case_studies', 'display_all_items', $related_industries);
  }
  // If there are results, build markup for them to pass to the template.
  $case_study_item_output = [];
  $viewBuilder = \Drupal::entityTypeManager()->getViewBuilder('node');
  foreach($case_study_view_results as $case_study_view_results_single) {
    $case_study_item_output[] = $viewBuilder->view($case_study_view_results_single->_entity, 'teaser');
  }
  $paragraph_variables['listings'] = $case_study_item_output;

  $variables['mhr_case_study_listing'] = $paragraph_variables;
}
