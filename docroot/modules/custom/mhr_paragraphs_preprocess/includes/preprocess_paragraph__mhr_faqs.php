<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_faqs (&$variables) {

  $variables['#attached']['library'][] = 'mhr/vsc_accordion';

  $paragraph = $variables['paragraph'];

  // field_faqs_title
  $field_faqs_title = 'FAQs';
  if (
    $paragraph->hasField('field_faqs_title') &&
    $paragraph->field_faqs_title->isEmpty() == FALSE
  ) {
    $field_faqs_title = $paragraph->field_faqs_title->value;
  }

  // field_faqs_subheading
  $field_faqs_subheading = null;
  if (
    $paragraph->hasField('field_faqs_subheading') &&
    $paragraph->field_faqs_subheading->isEmpty() == FALSE
  ) {
    $field_faqs_subheading = $paragraph->field_faqs_subheading->value;
  }

  $variables['faqs_output'] = [
    'faqs_output_title' => $field_faqs_title,
    'faqs_output_subheading' => $field_faqs_subheading,
  ];
}
