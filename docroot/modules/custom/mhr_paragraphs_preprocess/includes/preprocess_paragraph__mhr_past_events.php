<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_past_events (&$variables) {

  /** @var Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  $paragraph_variables = [];

  // field_stripe_heading
  $paragraph_variables['title'] = null;
  if (
    $paragraph->hasField('field_stripe_heading') &&
    $paragraph->field_stripe_heading->isEmpty() === FALSE
  ) {
    $paragraph_variables['title'] = $paragraph->field_stripe_heading->value;
  }

  // Get the items to be displayed.
  $mhr_past_events_view_results = views_get_view_result('mhr_past_events', 'default');

  // If there are results, build markup for them, and create a carousel.
  $mhr_past_events_carousel_render_array = null;
  if (count($mhr_past_events_view_results) > 0) {
    $mhr_past_events_cards = [];
    $viewBuilder = \Drupal::entityTypeManager()->getViewBuilder('node');
    foreach($mhr_past_events_view_results as $mhr_past_events_view_results_single) {
      // Get a render array for the result's node, in 'teaser' display.
      // This will run the nodes through the node--event--teaser.html.twig template.
      $mhr_past_events_cards[] = $viewBuilder->view($mhr_past_events_view_results_single->_entity, 'teaser');
    }
    // Set up carousel settings for Swiper.
    $carousel_settings = json_encode([
      'slidesPerGroup' => 1,
      'slidesPerView' => 1,
      'spaceBetween' => 24,
      'speed' => 250,
      'watchSlidesProgress' => true,
      'breakpoints' => [
        '768' => [
          'slidesPerGroup' => 2,
          'slidesPerView' => 2,
        ],
        '992' => [
          'slidesPerGroup' => 2,
          'slidesPerView' => 2,
        ],
        '1200' => [
          'slidesPerGroup' => 3,
          'slidesPerView' => 3,
        ],
      ],
    ]);
    $mhr_past_events_carousel_render_array = [
      '#theme' => 'vsc_carousel_grid',
      '#carousel_items' => $mhr_past_events_cards,
      '#carouselSettings' => $carousel_settings,
      '#vsc_carousel_prev_icon' => 'arrow-right',
      '#vsc_carousel_next_icon' => 'arrow-right',
    ];
    $mhr_past_events_carousel_render_array['#attached']['library'][] = 'mhr/mhr-carousel';
  }

  $paragraph_variables['mhr_past_events_carousel'] = $mhr_past_events_carousel_render_array;

  $variables['mhr_past_events'] = $paragraph_variables;
}
