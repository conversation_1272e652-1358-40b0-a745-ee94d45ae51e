<?php

use Dr<PERSON>al\Core\Url;

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_teaser_card_cta (&$variables) {

  $paragraph = $variables['paragraph'];

  $paragraph_data = [];

  // $field_mhr_tcc_appearance
  $field_mhr_tcc_appearance = 'flow'; // Set up default value.
  if (
    $paragraph->hasField('field_mhr_tcc_appearance') &&
    $paragraph->field_mhr_tcc_appearance->isEmpty() == FALSE
  ) {
    $field_mhr_tcc_appearance = $paragraph->field_mhr_tcc_appearance->value;
  }
  $paragraph_data['mhr_teaser_card_cta_appearance'] = $field_mhr_tcc_appearance;

  // field_mhr_tcc_heading
  $field_mhr_tcc_heading = null;
  if (
    $paragraph->hasField('field_mhr_tcc_heading') &&
    $paragraph->field_mhr_tcc_heading->isEmpty() == FALSE
  ) {
    $field_mhr_tcc_heading = $paragraph->field_mhr_tcc_heading->value;
  }
  $paragraph_data['mhr_teaser_card_cta_title'] = $field_mhr_tcc_heading;

  // field_mhr_tcc_subheading
  $field_mhr_tcc_subheading = null;
  if (
    $paragraph->hasField('field_mhr_tcc_subheading') &&
    $paragraph->field_mhr_tcc_subheading->isEmpty() == FALSE
  ) {
    $field_mhr_tcc_subheading = $paragraph->field_mhr_tcc_subheading->value;
  }
  $paragraph_data['mhr_teaser_card_cta_subheading'] = $field_mhr_tcc_subheading;

  // field_mhr_tcc_button
  $field_mhr_tcc_button = null;
  if (
    $paragraph->hasField('field_mhr_tcc_button') &&
    $paragraph->field_mhr_tcc_button->isEmpty() == FALSE
  ) {
    $field_mhr_tcc_button_data = $paragraph->field_mhr_tcc_button->getValue()[0];
    if (isset($field_mhr_tcc_button_data['uri'])) {
      $field_mhr_tcc_button_link = Url::fromUri($field_mhr_tcc_button_data['uri'])->toString();
      if (isset($field_mhr_tcc_button_data['title'])) {
        $field_mhr_tcc_button_text = $field_mhr_tcc_button_data['title'];
      } else {
        $field_mhr_tcc_button_text = $field_mhr_tcc_button_link;
      }
      $field_mhr_tcc_button = [
        'label' => $field_mhr_tcc_button_text,
        'url'   => $field_mhr_tcc_button_link,
      ];
    }
  }
  $paragraph_data['mhr_teaser_card_cta_button'] = $field_mhr_tcc_button;

  // field_mhr_tcc_cards
  $field_mhr_tcc_cards = [];
  if (
    $paragraph->hasField('field_mhr_tcc_cards') &&
    $paragraph->field_mhr_tcc_cards->isEmpty() == FALSE
  ) {
    // Process mhr_teaser_card_cta_card components referenced by field_mhr_tcc_cards.
    foreach($paragraph->field_mhr_tcc_cards->referencedEntities() as $single_card) {

      // field_mhr_tcc_card_subheading
      $field_mhr_tcc_card_subheading = null;
      if (
        $single_card->hasField('field_mhr_tcc_card_subheading') &&
        $single_card->field_mhr_tcc_card_subheading->isEmpty() == FALSE
      ) {
        $field_mhr_tcc_card_subheading = $single_card->field_mhr_tcc_card_subheading->value;
      }

      // field_mhr_tcc_card_text
      $field_mhr_tcc_card_text = null;
      if (
        $single_card->hasField('field_mhr_tcc_card_text') &&
        $single_card->field_mhr_tcc_card_text->isEmpty() == FALSE
      ) {
        $field_mhr_tcc_card_text = $single_card->field_mhr_tcc_card_text->value;
      }

      // field_mhr_tcc_card_image
      $field_mhr_tcc_card_image = null;
      if (
        $single_card->hasField('field_mhr_tcc_card_image') &&
        $single_card->field_mhr_tcc_card_image->isEmpty() == FALSE
      ) {
        $field_mhr_tcc_card_image_data = _mhr_get_image_data($single_card->field_mhr_tcc_card_image);
        if ($field_mhr_tcc_card_image_data) {
          $field_mhr_tcc_card_image = [
            '#height' => $field_mhr_tcc_card_image_data['image_height'],
            '#responsive_image_style_id' => 'aspect_ratio_32_33',
            '#theme' => 'responsive_image',
            '#uri' => $field_mhr_tcc_card_image_data['image_uri'],
            '#width' => $field_mhr_tcc_card_image_data['image_width'],
          ];
        }
      }

      // field_mhr_tcc_card_link
      $field_mhr_tcc_card_link = null;
      if (
        $single_card->hasField('field_mhr_tcc_card_link') &&
        $single_card->field_mhr_tcc_card_link->isEmpty() == FALSE
      ) {
        $field_mhr_tcc_card_link_data = $single_card->field_mhr_tcc_card_link->getValue()[0];
        if (isset($field_mhr_tcc_card_link_data['uri'])) {
          $field_mhr_tcc_card_link = Url::fromUri($field_mhr_tcc_card_link_data['uri'])->toString();
        }
      }

      $field_mhr_tcc_cards[] = twig_render_template(
        \Drupal::service('extension.list.theme')->getPath('mhr') . '/templates/mhr-card.html.twig',
        [
          'card' => [
            'card_body' => $field_mhr_tcc_card_text,
            'card_custom_wrapper_classes_array' => $field_mhr_tcc_appearance == 'carousel' || $field_mhr_tcc_appearance == 'grid' ? ['mhr-teaser-card-cta__card mhr-card-full-background-image'] : ['mhr-teaser-card-cta__card'],
            'card_heading' => $field_mhr_tcc_card_subheading,
            'card_image' => $field_mhr_tcc_card_image,
            'card_link' => [
              'url' => $field_mhr_tcc_card_link,
            ],
          ],
          'theme_hook_original' => 'not-applicable',
          'directory' => '',
        ]
      );
    }
  }
  $paragraph_data['mhr_teaser_card_cta_cards'] = $field_mhr_tcc_cards;

  // If the 'Appearance' selection is 'carousel', set up additional variables for
  // use by the template.
  if ($field_mhr_tcc_appearance == 'carousel') {
    $mhr_teaser_card_cta_carousel_render_array = null;
    // Set up carousel settings for Swiper.
    $carousel_settings = _mhr_enhanced_carousel_swiper_settings();
    $mhr_teaser_card_cta_carousel_render_array = [
      '#theme' => 'vsc_carousel_grid',
      '#carousel_items' => $field_mhr_tcc_cards,
      '#carouselSettings' => $carousel_settings,
      '#vsc_carousel_prev_icon' => 'arrow-right',
      '#vsc_carousel_next_icon' => 'arrow-right',
    ];
    $mhr_teaser_card_cta_carousel_render_array['#attached']['library'][] = 'mhr/mhr-carousel';
    $paragraph_data['mhr_teaser_card_cta_carousel'] = $mhr_teaser_card_cta_carousel_render_array;
  }

  $variables['mhr_teaser_card_cta'] = $paragraph_data;
}
