<?php

use <PERSON>upal\Core\Render\Markup;

/**
 * Implements hook_preprocess_paragraph_HOOK().
 *
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__embed_js_widget(&$variables) {

  $paragraph = $variables['paragraph'];

  if ($paragraph->hasField('field_js_embed_code')) {
    $field_js_embed_code = $paragraph->get('field_js_embed_code');
    $variables['raw_embed_code'] = $field_js_embed_code->getString();
  }
  $variables['embed_js_widget_admin_view'] = Markup::create("<div>Embedded code:<br></div>");
}
