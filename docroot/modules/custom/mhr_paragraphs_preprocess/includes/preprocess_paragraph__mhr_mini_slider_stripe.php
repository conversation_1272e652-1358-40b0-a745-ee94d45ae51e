<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_mini_slider_stripe (&$variables) {

  $variables['#attached']['library'][] = 'mhr/mhr-carousel';

  /** @var Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  // field_mini_slider_title
  $field_mini_slider_title = null;
  if (
    $paragraph->hasField('field_mini_slider_title') &&
    $paragraph->field_mini_slider_title->isEmpty() === FALSE
  ) {
    $field_mini_slider_title = $paragraph->field_mini_slider_title->value;
  }

  // field_stripe_items
  $field_stripe_items = false;
  if (
    $paragraph->hasField('field_stripe_items') &&
    $paragraph->field_stripe_items->isEmpty() === FALSE
  ) {
    $field_stripe_items_items = $paragraph->field_stripe_items;
    foreach($field_stripe_items_items as $field_stripe_items_items_single) {
      // Gather data for the item. Assumption is that the item is an
      // entity reference item, referencing mini_slider_item items.
      $item_paragraph = $field_stripe_items_items_single->entity;
      if (
        get_class($item_paragraph) !== 'Drupal\paragraphs\Entity\Paragraph' ||
        $item_paragraph->getType() !== 'mini_slider_item'
      ) {
        continue;
      }
      // mini_slider_item -> field_stripe_item
      $field_stripe_item = false;
      $field_stripe_item_data_url = null;
      $field_stripe_item_data_label = null;
      if (
        $item_paragraph->hasField('field_stripe_item') &&
        $item_paragraph->field_stripe_item->isEmpty() === FALSE
      ) {
        $field_stripe_item = $item_paragraph->field_stripe_item->getValue();
        $field_stripe_item_data = $field_stripe_item[0];
        $field_stripe_item_data_url = Drupal\Core\Url::fromUri($field_stripe_item_data['uri'])->toString();
        $field_stripe_item_data_label = $field_stripe_item_data['title'];
      }
      // Render item.
      // See https://www.jeffgeerling.com/blog/2019/rendering-twig-templates-programmatically-drupal-8
      // for details of twig_render_template.
      $field_stripe_items[] = twig_render_template(
        \Drupal::service('extension.list.theme')->getPath('mhr') . '/templates/paragraph--mini-slider-item.html.twig',
        [
          'mini_slider_item_link' => [
            'url' => $field_stripe_item_data_url,
            'label' => $field_stripe_item_data_label,
          ],
          'theme_hook_original' => 'not-applicable',
          'directory' => '',
        ]
      );
    }
  }

  $variables['carousel_items'] = $field_stripe_items;
  $variables['carousel_title'] = $field_mini_slider_title;
  $variables['set_as_grid_layout'] = false;
  $variables['vsc_carousel_prev_icon'] = 'arrow-right';
  $variables['vsc_carousel_next_icon'] = 'arrow-right';

  // Set up carousel settings for Swiper.
  $variables['carouselSettings'] = json_encode([
    'centerInsufficientSlides' => true,
    'slidesPerGroup' => 2,
    'slidesPerView' => 2,
    'spaceBetween' => 24,
    'speed' => 250,
    'watchSlidesProgress' => true,
    'breakpoints' => [
      '768' => [
        'slidesPerGroup' => 4,
        'slidesPerView' => 4,
      ],
      '992' => [
        'slidesPerGroup' => 4,
        'slidesPerView' => 4,
      ],
      '1200' => [
        'slidesPerGroup' => 5,
        'slidesPerView' => 5,
      ],
    ],
  ]);
}
