<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__hero_banner (&$variables) {
  // HELPDESK-2553: Use different view modes for background image based on 'shallow' choice.
  $paragraph = $variables['paragraph'];
  $background_image_display_mode = 'hero_banner';
  if (
    $paragraph->hasField('field_banner_depth') &&
    $paragraph->field_banner_depth->isEmpty() == FALSE &&
    $paragraph->field_banner_depth->value == 'shallow'
  ) {
    $background_image_display_mode = 'hero_banner_shallow';
  }
  // Change view mode used for the first field_background_image element being displayed.
  // There is only one item allowed for the field so this will work while that remains the case.
  // The different view modes have different responsive image styles configured, so the use
  // of each view mode results in a different reponsive image style being applied to the image.
  $variables['content']['field_background_image'][0]['#view_mode'] = $background_image_display_mode;
}
