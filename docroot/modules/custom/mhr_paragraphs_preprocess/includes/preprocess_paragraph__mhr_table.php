<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_table (&$variables) {

  $paragraph = $variables['paragraph'];

  $paragraph_data = [];

  // Table Content (field_mhr_table_content) – a Wysiwyg field that uses a specific text format
  // to only allow tables and completely plain text to be entered.
  $field_mhr_table_content = null;
  if (
    $paragraph->hasField('field_mhr_table_content') &&
    $paragraph->field_mhr_table_content->isEmpty() == FALSE
  ) {
    $field_mhr_table_content = [
      '#type' => 'inline_template',
      '#template' => $paragraph->field_mhr_table_content->value,
    ];
  }
  $paragraph_data['mhr_table_content'] = $field_mhr_table_content;

  $variables['mhr_table'] = $paragraph_data;
}
