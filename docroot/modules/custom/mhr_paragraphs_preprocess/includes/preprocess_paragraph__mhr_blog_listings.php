<?php

/**
 * Implements hook_preprocess_paragraph()
 */
function mhr_paragraphs_preprocess_preprocess_paragraph__mhr_blog_listings (&$variables) {

  /** @var Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  $paragraph_variables = [];

  // field_heading_above_blog_listing
  $paragraph_variables['title'] = null;
  if (
    $paragraph->hasField('field_heading_above_blog_listing') &&
    $paragraph->field_heading_above_blog_listing->isEmpty() === FALSE
  ) {
    $paragraph_variables['title'] = $paragraph->field_heading_above_blog_listing->value;
  }

  $paragraph_variables['listings'] = views_embed_view('mhr_blog_listings', 'default');

  $variables['mhr_blog_listings'] = $paragraph_variables;
}
