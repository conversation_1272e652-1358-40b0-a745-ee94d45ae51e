/*!
 * @fileOverview TouchSwipe - jQuery Plugin
 * @version 1.6.18
 * <AUTHOR> http://www.github.com/mattb<PERSON><PERSON>
 * @see https://github.com/mattbryson/TouchSwipe-Jquery-Plugin
 * @see http://labs.rampinteractive.co.uk/touchSwipe/
 * @see http://plugins.jquery.com/project/touchSwipe
 * @license
 * Copyright (c) 2010-2015 <PERSON>
 * Dual licensed under the MIT or GPL Version 2 licenses.
 ***This version modified to support Windows touch & click simultaneously***
 * https://github.com/justinputney/TouchSwipe-Jquery-Plugin
 */

!function(e){"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],e):"undefined"!=typeof module&&module.exports?e(require("jquery")):e(jQuery)}(function(e){"use strict";var n="left",t="right",r="up",i="down",l="in",o="out",a="none",u="auto",s="swipe",c="pinch",p="tap",f="doubletap",h="longtap",g="horizontal",d="vertical",w="all",v=10,T="start",y="move",E="end",m="cancel",x="ontouchstart"in document,S=window.navigator.msPointerEnabled&&!window.PointerEvent&&!x,b=("onpointerdown"in window||window.PointerEvent||window.navigator.msPointerEnabled)&&!x,O="TouchSwipe";function M(M,P){P=e.extend({},P);var D=x||b||!P.fallbackToMouseEvents,L=D?b?S?"MSPointerDown":"pointerdown":"touchstart":"mousedown",R=D?b?S?"MSPointerMove":"pointermove":"touchmove":"mousemove",k=D?b?S?"MSPointerUp":"pointerup touchend":"touchend":"mouseup",A=D?b?"mouseleave":"pointerleave":"mouseleave",I=b?S?"MSPointerCancel":"pointercancel":"touchcancel",U=0,j=null,N=null,H=0,_=0,q=0,C=1,Q=0,F=0,X=null,Y=e(M),V="start",W=0,z={},B=0,G=0,Z=0,J=0,K=0,$=null,ee=null;try{Y.on(L,ne),Y.on(I,ie)}catch(n){e.error("events not supported "+L+","+I+" on jQuery.swipe")}function ne(l){if(!0!==Y.data(O+"_intouch")&&!(e(l.target).closest(P.excludedElements,Y).length>0)){var o=l.originalEvent?l.originalEvent:l;if(!o.pointerType||"mouse"!=o.pointerType||0!=P.fallbackToMouseEvents){var a,u,s=o.touches,c=s?s[0]:o;return V=T,s?W=s.length:!1!==P.preventDefaultEvents&&l.preventDefault(),U=0,j=null,N=null,F=null,H=0,_=0,q=0,C=1,Q=0,(u={})[n]=De(n),u[t]=De(t),u[r]=De(r),u[i]=De(i),X=u,xe(),Oe(0,c),!s||W===P.fingers||P.fingers===w||ge()?(B=Ae(),2==W&&(Oe(1,s[1]),_=q=Re(z[0].start,z[1].start)),(P.swipeStatus||P.pinchStatus)&&(a=ue(o,V))):a=!1,!1===a?(ue(o,V=m),a):(P.hold&&(ee=setTimeout(e.proxy(function(){Y.trigger("hold",[o.target]),P.hold&&(a=P.hold.call(Y,o,o.target))},this),P.longTapThreshold)),be(!0),null)}}}function te(e){var s=e.originalEvent?e.originalEvent:e;if(V!==E&&V!==m&&!Se()){var c,p,f,h=s.touches,v=Me(h?h[0]:s);if(G=Ae(),h&&(W=h.length),P.hold&&clearTimeout(ee),V=y,2==W&&(0==_?(Oe(1,h[1]),_=q=Re(z[0].start,z[1].start)):(Me(h[1]),q=Re(z[0].end,z[1].end),z[0].end,z[1].end,F=C<1?o:l),C=(q/_*1).toFixed(2),Q=Math.abs(_-q)),W===P.fingers||P.fingers===w||!h||ge()){if(j=ke(v.start,v.end),function(e,l){if(!1===P.preventDefaultEvents)return;if(P.allowPageScroll===a)e.preventDefault();else{var o=P.allowPageScroll===u;switch(l){case n:(P.swipeLeft&&o||!o&&P.allowPageScroll!=g)&&e.preventDefault();break;case t:(P.swipeRight&&o||!o&&P.allowPageScroll!=g)&&e.preventDefault();break;case r:(P.swipeUp&&o||!o&&P.allowPageScroll!=d)&&e.preventDefault();break;case i:(P.swipeDown&&o||!o&&P.allowPageScroll!=d)&&e.preventDefault()}}}(e,N=ke(v.last,v.end)),p=v.start,f=v.end,U=Math.round(Math.sqrt(Math.pow(f.x-p.x,2)+Math.pow(f.y-p.y,2))),H=Le(),function(e,n){if(e==a)return;n=Math.max(n,Pe(e)),X[e].distance=n}(j,U),c=ue(s,V),!P.triggerOnTouchEnd||P.triggerOnTouchLeave){var T=!0;if(P.triggerOnTouchLeave){var x=this.getBoundingClientRect();T=function(e,n){return e.x>n.left&&e.x<n.right&&e.y>n.top&&e.y<n.bottom}(v.end,x)}!P.triggerOnTouchEnd&&T?V=ae(y):P.triggerOnTouchLeave&&!T&&(V=ae(E)),V!=m&&V!=E||ue(s,V)}}else ue(s,V=m);!1===c&&ue(s,V=m)}}function re(e){var n=e.originalEvent?e.originalEvent:e,t=n.touches;if(t){if(t.length&&!Se())return function(e){Z=Ae(),J=e.touches.length+1}(n),!0;if(t.length&&Se())return!0}return Se()&&(W=J),G=Ae(),H=Le(),pe()||!ce()?ue(n,V=m):P.triggerOnTouchEnd||!1===P.triggerOnTouchEnd&&V===y?(!1!==P.preventDefaultEvents&&!1!==e.cancelable&&e.preventDefault(),ue(n,V=E)):!P.triggerOnTouchEnd&&ye()?se(n,V=E,p):V===y&&ue(n,V=m),be(!1),null}function ie(){W=0,G=0,B=0,_=0,q=0,C=1,xe(),be(!1)}function le(e){var n=e.originalEvent?e.originalEvent:e;P.triggerOnTouchLeave&&ue(n,V=ae(E))}function oe(){Y.off(L,ne),Y.off(I,ie),Y.off(R,te),Y.off(k,re),A&&Y.off(A,le),be(!1)}function ae(e){var n=e,t=fe(),r=ce(),i=pe();return!t||i?n=m:!r||e!=y||P.triggerOnTouchEnd&&!P.triggerOnTouchLeave?!r&&e==E&&P.triggerOnTouchLeave&&(n=m):n=E,n}function ue(e,n){var t,r=e.touches;return(de()&&we()||we())&&(t=se(e,n,s)),(he()&&ge()||ge())&&!1!==t&&(t=se(e,n,c)),me()&&Ee()&&!1!==t?t=se(e,n,f):H>P.longTapThreshold&&U<v&&P.longTap&&!1!==t?t=se(e,n,h):1!==W&&x||!(isNaN(U)||U<P.threshold)||!ye()||!1===t||(t=se(e,n,p)),n===m&&ie(),n===E&&(r&&r.length||ie()),t}function se(a,u,g){var d;if(g==s){if(Y.trigger("swipeStatus",[u,j||null,U||0,H||0,W,z,N]),P.swipeStatus&&!1===(d=P.swipeStatus.call(Y,a,u,j||null,U||0,H||0,W,z,N)))return!1;if(u==E&&de()){if(clearTimeout($),clearTimeout(ee),Y.trigger("swipe",[j,U,H,W,z,N]),P.swipe&&!1===(d=P.swipe.call(Y,a,j,U,H,W,z,N)))return!1;switch(j){case n:Y.trigger("swipeLeft",[j,U,H,W,z,N]),P.swipeLeft&&(d=P.swipeLeft.call(Y,a,j,U,H,W,z,N));break;case t:Y.trigger("swipeRight",[j,U,H,W,z,N]),P.swipeRight&&(d=P.swipeRight.call(Y,a,j,U,H,W,z,N));break;case r:Y.trigger("swipeUp",[j,U,H,W,z,N]),P.swipeUp&&(d=P.swipeUp.call(Y,a,j,U,H,W,z,N));break;case i:Y.trigger("swipeDown",[j,U,H,W,z,N]),P.swipeDown&&(d=P.swipeDown.call(Y,a,j,U,H,W,z,N))}}}if(g==c){if(Y.trigger("pinchStatus",[u,F||null,Q||0,H||0,W,C,z]),P.pinchStatus&&!1===(d=P.pinchStatus.call(Y,a,u,F||null,Q||0,H||0,W,C,z)))return!1;if(u==E&&he())switch(F){case l:Y.trigger("pinchIn",[F||null,Q||0,H||0,W,C,z]),P.pinchIn&&(d=P.pinchIn.call(Y,a,F||null,Q||0,H||0,W,C,z));break;case o:Y.trigger("pinchOut",[F||null,Q||0,H||0,W,C,z]),P.pinchOut&&(d=P.pinchOut.call(Y,a,F||null,Q||0,H||0,W,C,z))}}return g==p?u!==m&&u!==E||(clearTimeout($),clearTimeout(ee),Ee()&&!me()?(K=Ae(),$=setTimeout(e.proxy(function(){K=null,Y.trigger("tap",[a.target]),P.tap&&(d=P.tap.call(Y,a,a.target))},this),P.doubleTapThreshold)):(K=null,Y.trigger("tap",[a.target]),P.tap&&(d=P.tap.call(Y,a,a.target)))):g==f?u!==m&&u!==E||(clearTimeout($),clearTimeout(ee),K=null,Y.trigger("doubletap",[a.target]),P.doubleTap&&(d=P.doubleTap.call(Y,a,a.target))):g==h&&(u!==m&&u!==E||(clearTimeout($),K=null,Y.trigger("longtap",[a.target]),P.longTap&&(d=P.longTap.call(Y,a,a.target)))),d}function ce(){var e=!0;return null!==P.threshold&&(e=U>=P.threshold),e}function pe(){var e=!1;return null!==P.cancelThreshold&&null!==j&&(e=Pe(j)-U>=P.cancelThreshold),e}function fe(){return!P.maxTimeThreshold||!(H>=P.maxTimeThreshold)}function he(){var e=ve(),n=Te(),t=null===P.pinchThreshold||Q>=P.pinchThreshold;return e&&n&&t}function ge(){return!!(P.pinchStatus||P.pinchIn||P.pinchOut)}function de(){var e=fe(),n=ce(),t=ve(),r=Te();return!pe()&&r&&t&&n&&e}function we(){return!!(P.swipe||P.swipeStatus||P.swipeLeft||P.swipeRight||P.swipeUp||P.swipeDown)}function ve(){return W===P.fingers||P.fingers===w||!x}function Te(){return 0!==z[0].end.x}function ye(){return!!P.tap}function Ee(){return!!P.doubleTap}function me(){if(null==K)return!1;var e=Ae();return Ee()&&e-K<=P.doubleTapThreshold}function xe(){Z=0,J=0}function Se(){var e=!1;Z&&(Ae()-Z<=P.fingerReleaseThreshold&&(e=!0));return e}function be(e){Y&&(!0===e?(Y.on(R,te),Y.one(k,function(e){re(e)}),A&&Y.one(A,le)):Y.off(R,te,!1),Y.data(O+"_intouch",!0===e))}function Oe(e,n){var t={start:{x:0,y:0},last:{x:0,y:0},end:{x:0,y:0}};return t.start.x=t.last.x=t.end.x=n.pageX||n.clientX,t.start.y=t.last.y=t.end.y=n.pageY||n.clientY,z[e]=t,t}function Me(e){var n=void 0!==e.identifier?e.identifier:0,t=function(e){return z[e]||null}(n);return null===t&&(t=Oe(n,e)),t.last.x=t.end.x,t.last.y=t.end.y,t.end.x=e.pageX||e.clientX,t.end.y=e.pageY||e.clientY,t}function Pe(e){if(X[e])return X[e].distance}function De(e){return{direction:e,distance:0}}function Le(){return G-B}function Re(e,n){var t=Math.abs(e.x-n.x),r=Math.abs(e.y-n.y);return Math.round(Math.sqrt(t*t+r*r))}function ke(e,l){if(u=l,(o=e).x==u.x&&o.y==u.y)return a;var o,u,s=function(e,n){var t=e.x-n.x,r=n.y-e.y,i=Math.atan2(r,t),l=Math.round(180*i/Math.PI);return l<0&&(l=360-Math.abs(l)),l}(e,l);return s<=45&&s>=0?n:s<=360&&s>=315?n:s>=135&&s<=225?t:s>45&&s<135?i:r}function Ae(){return(new Date).getTime()}this.enable=function(){return this.disable(),Y.on(L,ne),Y.on(I,ie),Y},this.disable=function(){return oe(),Y},this.destroy=function(){oe(),Y.data(O,null),Y=null},this.option=function(n,t){if("object"==typeof n)P=e.extend(P,n);else if(void 0!==P[n]){if(void 0===t)return P[n];P[n]=t}else{if(!n)return P;e.error("Option "+n+" does not exist on jQuery.swipe.options")}return null}}e.fn.swipe=function(n){var t=e(this),r=t.data(O);if(r&&"string"==typeof n){if(r[n])return r[n].apply(r,Array.prototype.slice.call(arguments,1));e.error("Method "+n+" does not exist on jQuery.swipe")}else if(r&&"object"==typeof n)r.option.apply(r,arguments);else if(!(r||"object"!=typeof n&&n))return function(n){!n||void 0!==n.allowPageScroll||void 0===n.swipe&&void 0===n.swipeStatus||(n.allowPageScroll=a);void 0!==n.click&&void 0===n.tap&&(n.tap=n.click);n||(n={});return n=e.extend({},e.fn.swipe.defaults,n),this.each(function(){var t=e(this),r=t.data(O);r||(r=new M(this,n),t.data(O,r))})}.apply(this,arguments);return t},e.fn.swipe.version="1.6.18",e.fn.swipe.defaults={fingers:1,threshold:75,cancelThreshold:null,pinchThreshold:20,maxTimeThreshold:null,fingerReleaseThreshold:250,longTapThreshold:500,doubleTapThreshold:200,swipe:null,swipeLeft:null,swipeRight:null,swipeUp:null,swipeDown:null,swipeStatus:null,pinchIn:null,pinchOut:null,pinchStatus:null,click:null,tap:null,doubleTap:null,longTap:null,hold:null,triggerOnTouchEnd:!0,triggerOnTouchLeave:!1,allowPageScroll:"auto",fallbackToMouseEvents:!0,excludedElements:".noSwipe",preventDefaultEvents:!0},e.fn.swipe.phases={PHASE_START:T,PHASE_MOVE:y,PHASE_END:E,PHASE_CANCEL:m},e.fn.swipe.directions={LEFT:n,RIGHT:t,UP:r,DOWN:i,IN:l,OUT:o},e.fn.swipe.pageScroll={NONE:a,HORIZONTAL:g,VERTICAL:d,AUTO:u},e.fn.swipe.fingers={ONE:1,TWO:2,THREE:3,FOUR:4,FIVE:5,ALL:w}});