/*
	AnythingSlider v1.8+ theme for in5
	Based on a theme by <PERSON>
*/
#slider {
	width:1024px;
	height:768px;
	list-style:none;
	/* Prevent FOUC (see FAQ page) and keep things readable if javascript is disabled */
	overflow-y:auto;
	overflow-x:hidden;
}

 /*=================================
 Default state (no keyboard focus)
 ==================================*/
/* Overall Wrapper */
.anythingSlider-in5 {
	margin:0 auto;
	/* 30px right & left padding for the navigation arrows */
	padding:0 30px;
}
/* slider window - top & bottom borders, default state */
.anythingSlider-in5 .anythingWindow {
	border-top:0; border-bottom:0;
}

/*
 =================================
 Active State (has keyboard focus)
 =================================
*/
/* slider window - top & bottom borders, active state */
.anythingSlider-in5.activeSlider .anythingWindow { border-color:transparent; }


/***********************
COMMON SLIDER STYLING
***********************/
/* Overall Wrapper */
.anythingSlider {
	display:block;
	overflow:visible !important;
	position:relative;
}
/* anythingSlider viewport window */
.anythingSlider .anythingWindow {
	overflow:hidden;
	position:relative;
	width:100%;
	height:100%;
}
/* anythingSlider base (original element) */
.anythingSlider .anythingBase {
	background:transparent;
	list-style:none;
	position:absolute;
	overflow:visible !important;
	top:0;
	margin:0;
	padding:0;
}

/* all panels inside the slider; horizontal mode */
.anythingSlider .panel {
	background:transparent;
	display:block;
	overflow:hidden;
	float:left;
	padding:0;
	margin:0;
}
/* vertical mode */
.anythingSlider .vertical .panel { float:none; }

/* fade mode */
.anythingSlider .fade .panel {
	float:none;
	position:absolute;
	top:0;
	left:0;
	z-index:-1;
}
/* fade mode active page - visible & on top */
.anythingSlider .fade .activePage { z-index:0; }

/***********************
  RTL STYLING
 ***********************/
/* slider autoplay right-to-left, reverse order of nav links to look better */
.anythingSlider.rtl .panel {
	float:right;
}
