/* 	Begin Modified Meyer Reset */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li,
fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary, time, mark, audio, video {
	margin:0; padding:0; border:0; font-size:100%; font:inherit; vertical-align:baseline;
}
/* HTML5 reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display:block;
}
body { line-height:1; letter-spacing:-0.25px; overscroll-behavior:contain;}
#container:not([style*="zoom"]) { -webkit-text-size-adjust:none;}
table { border-collapse:collapse; border-spacing:0; }
th { font-weight:normal; text-align:left; }
ul { list-style-type:none; }
.page ol { list-style-position:outside; margin-left:1em; }
/*.page ol > li, .page  ul > li { text-indent:-1em; padding-left:1em; }*/
.page .singleline li, .page .singleline li { text-indent:inherit; padding:0; }
.page ol > li > *, .page ul > li > * { text-indent:initial; padding-left:initial; }
button { background:transparent;border:none; }
/*General styles*/
html, body, #container-wrap, #container { height:100%; }
#container-wrap { position:absolute; top:0; left:0; }
#container-wrap, #container { min-height:100%; width:100%; background-color:transparent; z-index:0;}
b { font-weight:bold; }
i { font-style:italic; }
a { color:inherit; text-decoration:inherit; }
input,select,textarea { font-size:inherit; }
p:first-child { margin-top:inherit !important; }
*{ outline:none; animation-fill-mode:both !important; -webkit-animation-fill-mode:both !important; }
[data-onclick],[onclick],[onrollover],[data-fixed-action],[aria-details],button,.flipwrap{ cursor:pointer; pointer-events:auto; }

body[class^='scaled-none'],body[class*=' scaled-none']{ overflow:auto; }
body[class^='scaled-width'],body[class*=' scaled-width']{ overflow-x:hidden; }
body[class^='scaled-height'],body[class*=' scaled-height']{ overflow-y:hidden; }
body[class^='scaled-best'],body[class*=' scaled-best']{ overflow:hidden; }
body[class^='scaled-best'][data-page-mode^='c'],body[class*=' scaled-best'][data-page-mode^='c']{ overflow:auto; }
body[class^='scaled-width'][data-page-mode^='csh'],body[class*=' scaled-width'][data-page-mode^='csh']{ overflow-x:auto; }
body[class^='scaled-height'][data-page-mode^='csv'],body[class*=' scaled-height'][data-page-mode^='csv']{ overflow-y:auto; }
body[data-scaled-to='w'] #container{ height:auto; min-height:auto; }
body[data-scaled-to='w'][data-page-mode^='cs'] #container{ position:absolute; }
body[data-scaled-to='w'][data-page-mode^='csv'] .pages { padding:0;}
body.zoomed:not([class*='scaled-']) #container { position:absolute; }
body[class*='scaled-'] .pages.paper-vertical{ margin:0; }
body[class^='zoomed'],body[class*=' zoomed']{ overflow-x:auto;overflow-y:auto; }
body[data-scaled-to] .anythingSlider-in5, body[data-scaled-to] #container > .page {padding:0;margin:0;}
body[data-scaled-to] #in5footer { position:fixed; bottom:0;}
/*body[data-scaled-to] * { -webkit-backface-visibility: hidden; }*/ /*fix blurry imgs*/
html.ios body[class*='scaled-'] #container { position:absolute; top:0; left:0; }
body.tall-page {overflow-y:auto;}
.liquid .page.tall-page {height:auto;}

.paper-vertical:after {
    position:relative;
    content:' ';
    display:block;
    height:50px;
    clear:both;
}
body[data-scaled-to="h"] .paper-vertical:after { display:none; }
body[data-scaled-to='h'] .paper-horizontal { margin:0; }
body[data-scaled-to='h'] .paper-horizontal .page { margin-top:0; }

#in5footer,#demoNote,#loadIndicator{
	min-width:100%;
	color:#fff;
	background:#666;
	font-family:"Trebuchet MS", "Lucida Grande", Helvetica, sans-serif;
	text-align:center;
	font-size:15px;
	box-sizing:border-box; 
	-moz-box-sizing:border-box;
}

#loadIndicator{
	width:100%; padding:6px; position:fixed;
	top:0; left:0; margin:auto;
    box-shadow:0 2px 4px rgba(0, 0, 0, 0.3);
    transition:all 1s ease-in .5s;
    opacity:1; z-index:99;
}
body.loaded #loadIndicator {
	opacity:0;
	transform:translateY(-36px);
	-webkit-transform:translateY(-36px);
	display:none\9;/*old IE*/
}
#loadIndicator img {
	vertical-align:text-bottom; width:18px;
}
html.local #loadIndicator, html.dps #loadIndicator, html.baker #loadIndicator { display:none; }

#in5footer{
	height:26px; line-height:26px; clear:both; position:relative; margin-top:-26px;
}

#demoNote {
    right:0; bottom:0; margin:auto;
    height:96px !important;
    padding:36px 10px;
}

#in5footer a, #demoNote a {
	text-decoration:none; color:#99ccff;
}
svg-img{display:block;position:absolute;}
.win-safari svg-img{width:100%;}
svg-img{width:100%\9;}
.spread-shift { position:absolute; }
.pageItem, svg { position:absolute; top:0; left:0; }
svg { overflow:visible; }
.pageItem {
	display:block;
	width:auto !important; height:auto !important;
	box-sizing:border-box; 
	-moz-box-sizing:border-box;
	-webkit-tap-highlight-color:transparent;
	padding:0;
}
.pageItem.hd/*, .pageItem img.hd*/  {
	-ms-transform:scale(.5);
    -ms-transform-origin:0 0 0;
    -webkit-transform:scale(.5);
    -webkit-transform-origin:0 0 0;
    transform:scale(.5);
    transform-origin:0 0 0;
}
@media screen and (-webkit-min-device-pixel-ratio:0) and (min-resolution:.001dpcm) {
    .pageItem { image-rendering: -webkit-optimize-contrast; }
}
@media not all and (min-resolution:.001dpcm)
{ @supports (-webkit-appearance:none) and (stroke-color:transparent) {
    .pageItem { image-rendering: optimizeSpeed; }
}}
@-moz-document url-prefix() {
  .pageItem { image-rendering: optimizeQuality; }
}
.svg-wrap { width:inherit !important; height:inherit !important; }
.pageItem p img { vertical-align:text-top; }
.pageItem p input, .pageItem p button {display:inline;}
.pageItem p .hidden, .details {visibility:hidden; }
.pageItem.group[data-ani] { will-change: transform; }
#colorbox .details { visibility:visible; }
.details {max-width:550px;font-size:18px;font-family:sans-serif;padding:0 24px;line-height:150%;left:0;right:0;margin:auto;}
.details p {margin-top:.5em;}
.details h1,.details h2,.details h3,.details h4,.details h5, .details strong {font-weight:bold;margin:.7em 0;}
.details em { font-style:italic; }
.details h1{font-size:150%;} .details h2{font-size:140%;} .details h3{font-size:130%;}
.details a {color:#0099ff;text-decoration:underline}
.details a:hover {text-decoration:none;}
@media (max-device-width:812px){ .details { font-size:32px; } }
html.ios .iframe-container { overflow:auto; -webkit-overflow-scrolling:touch; }
iframe[scrolling="auto"] { overflow:auto; }
iframe[scrolling="yes"] { overflow:scroll; }
iframe[scrolling="no"] { overflow:hidden; }
.page-scale-wrap { transform:translateZ(0); }
.page, .page-scale-wrap {
	position:relative; float:left; overflow:hidden;
}
.pages {
	list-style:none outside none;
	padding:0; margin:0 auto ;
}	
#container > .page { /*center single page baker*/
    position:absolute; bottom:0;
    left:0; right:0; top:0; margin:0 auto;
}
body:not(.zoomed,.responsive) #container/*, .page-scale-wrap*/ {/*desktop scaling transitions*/
	-webkit-transition:-webkit-transform .5s;
	transition:transform .5s;
}
html.multifile body #container { opacity:0; }
html.multifile body.loaded #container {/*do show scaling of multi-page*/
	opacity:1;
	-ms-transition:opacity .1s step-start .1s !important;
	-webkit-transition:opacity .1s step-start .1s !important;
	transition:opacity .1s step-start .1s !important;
}
html.multifile.fade body.loaded #container {
	-ms-transition:opacity .6s !important;
	-webkit-transition:opacity .6s !important;
	transition:opacity .6s !important;
}

.textColumn { float:left; }
.singleline p { line-height:1em !important; position:absolute;width:100%;/*fix webkit display issue*/ }
p.col-break-before::before {
	content:'';
    height:0px; width:100%;
    display:block; margin-bottom:100%;
}
.scroll, .scroll-vert, .scroll-horiz { -webkit-overflow-scrolling:touch; }/*was broken in iOS, turn off if scroll not working*/
.scroll { overflow:auto; }
.scroll-vert { overflow-y:auto; overflow-x:hidden; }
.scroll-horiz { overflow-x:auto; overflow-y:hidden; }
.scroll-vert::-webkit-scrollbar { -webkit-appearance:none; width:7px; } /*add back scrollbar for webkit*/
.scroll-vert::-webkit-scrollbar-thumb { border-radius:4px; background-color:rgba(0,0,0,.5); }
.hidescroll::-webkit-scrollbar { display:none; }
.hidescroll { -ms-overflow-style:none; overflow:-moz-scrollbars-none;/*old FF*/ }
.pulltab-left, .pulltab-left > * {
	-moz-transform:scaleX(-1);
    -webkit-transform:scaleX(-1);
    -o-transform:scaleX(-1);
    transform:scaleX(-1);
}
.pulltab-top, .pulltab-top > * {
	-moz-transform:scaleY(-1);
    -webkit-transform:scaleY(-1);
    -o-transform:scaleY(-1);
    transform:scaleY(-1);
}
object, embed, img, a { outline:0; }
.liquid svg, .liquid .page-scale-wrap { height:100%; width:100%; }
.noPlugin {
	width:100%; height:100%; padding:6px;
	background:#ccc; border:1px solid #666;
}

.paper-vertical, .paper-horizontal { padding:0 8px; }
.flip { 
	overflow:hidden; 
	-webkit-transition:transform 0.3s ease-in-out;
	-ms-transition:transform 0.3s ease-in-out;
	transition:transform 0.3s ease-in-out;
}
.turn-page-wrapper{
	will-change:transform;
}
.turn-page {
	-moz-box-shadow:0px 0px 20px rgba(0,0,0,.7);
	-webkit-box-shadow:0px 0px 20px rgba(0,0,0,.7);
    box-shadow:0px 0px 20px rgba(0,0,0,.7);
}
.turn-page:not(.activePage) .pageItem[data-ani] { /*fix 2x animation*/
  	opacity:0;
  	animation:none !important;
}
.paper-horizontal {
	position:absolute; overflow-y:visible; overflow-x:hidden;
	margin-top:auto; margin-bottom:auto;
    top:0; bottom:0;
}

.paper-vertical .page, .paper-horizontal .page {
	border:1px solid #bbb;	
	margin:4px 4px 4px 0;
	-moz-box-shadow:0px 0px 0px #999;
	-webkit-box-shadow:0px 0px 5px #999;
	box-shadow:0px 0px 5px #999;
}

html[data-dir="rtl"] .paper-horizontal .page { float:right; }

.one-page-scroll .page {
	border:none;
	margin:0;
	-moz-box-shadow:none;
	-webkit-box-shadow:none;
	box-shadow:none;
}

.mejs-mediaelement { overflow:hidden; }
.mejs-container{position:relative;background:none;}
.mejs-none .mejs-overlay-loading, .mejs-none .mejs-overlay-button, .mejs-none .mejs-controls, .mejs-bigplay .mejs-controls {display:none !important;}
.mejs-uncon video { object-fit:fill; }
.mejs-coverfit video { object-fit:cover; }
.mejs-ccsize-large .mejs-captions-layer {font-size:20px;}
.mejs-ccsize-xlarge .mejs-captions-layer {font-size:26px;}
.mejs-video .mejs-controls { transform:translateZ(0); }

.mejs-no-controls { display:none; }
.vid-scaled .mejs-layers, .vid-scaled .mejs-controls {
	-webkit-transform:translateZ(0);
	transform:translateZ(0);
}

.liquid .page, .page.liquid {
	position:absolute; width:100%; height:100%;
	overflow:hidden; display:none;
}

.page.liquid { /*for multi-file*/
    display:block;
}
.liquid .page.activePage {display:block; }
.liquid video {width:100%; height:100%; }
.liquid .slideshow img { object-fit:contain; }

.in5-highlight {
	background:rgba(255,255,0,.6);
	mix-blend-mode:multiply;
	pointer-events:none;
	animation-name:in5-highlight-in,in5-highlight-in;
	animation-direction:normal,reverse;
	animation-delay:3s,3s;
	animation-duration:.5s,1s;
	animation-iteration-count:1,1;
}
@keyframes in5-highlight-in{ from{ opacity:0 } to { opacity: 1} }
.hidden { display:none !important; }
.skip-link-wrap { z-index: 1; position: fixed; }
[data-reader-only="1"]{
	transform: translateY(-100vh) !important;
	z-index:-1;
}
[data-reader-only="1"]:focus, [data-reader-only="1"]:active {
	transform: translateY(0%) !important;
	z-index:1;
}
.mso, .group, .mso > .state { pointer-events:none; }
.mso > .state * { pointer-events: inherit; }
.mso > .state.active * { pointer-events:auto; }
[data-tapstart="1"], [data-useswipe="1"], .group > * { pointer-events:auto; }
.mso > .state { display:block; opacity:0; width:100% !important; height:inherit !important; position:absolute; z-index:-1; }
.liquid .mso > .state { height:100% !important; }
.liquid label > .state {
    width:100% !important;
    height:100% !important;
}
.mso > .state.active, .mso > .state.transition { display:block; opacity:1; z-index:0; }
.mso.slideshow.seq, .panzoom, .fauxgroup { overflow:hidden; }
.panzoom { cursor:move; cursor:-moz-grab; cursor:-webkit-grab; }
.panzoom > .content {
	-ms-transform:none;
    -ms-transform-origin:auto;
    -webkit-transform:none;
    -webkit-transform-origin:auto;
    transform:none;
    transform-origin:auto;
}
.panzoom:not(.dragging) > .content {
	transition:all .3s;
}

button .state {width:100% !important; height:100% !important;}
button .state.btn-on, button .state.btn-down { display:none; }
html:not(.ios) button.has-on:hover .state.btn-on { display:block; }
html:not(.ios) button.has-on:hover .state.btn-off, html:not(.ios) button.has-on:hover .state.btn-down { display:none; }
html:not(.zz) button.has-down .state { pointer-events:none; }
html:not(.zz) button.has-down:active .state.btn-down { display:block; }
html:not(.zz) button.has-down:active .state.btn-off, html:not(.zz) button.has-down:active .state.btn-on { display:none; }

.align-vert { display:table; }
[class^="valign"] {
	position:relative;
	display:table-cell;
	vertical-align:middle;
	columns:inherit;
}
.valign-bottom { vertical-align:bottom; }
[class^="valign"] > p { position:relative; }

.flex-image, .fill-image { background-repeat:no-repeat; }
.flex-image { background-size:contain; background-position:center; }
.fill-image { background-size:cover; }

/*tabs*/
p.tabs, p.tabs > a { 
	display:-webkit-box;
	display:-moz-box;
	display:-ms-flexbox;
	display:-webkit-flex;
	display:flex;
	flex-wrap:wrap;
}
p.tabs > a {
	width:100%;
	width:-moz-available;
	width:-webkit-fill-available;
}
p.tabs span.tabbed-right { 
	-ms-flex-grow:1; 
	-webkit-flex-grow:1; 
	flex-grow:1; 
	text-align:right; 
}
p.tabs span.tabbed-center { 
	-ms-flex-grow:50; 
	-webkit-flex-grow:50; 
	flex-grow:50; 
	text-align:center; 
}
span.tabbed-standard { text-indent:2ex;padding-left:2ex; }
p.no-start-indent { text-indent:0px !important; }

/*anchor object styles*/
.ao-noTextWrap { position:absolute; }
.ao-alignLeft, .ao-leftFullWidth { float:left; position:relative; }
.ao-alignRight, .ao-rightFullWidth { float:right; transform-origin:90% 50%; -webkit-transform:90% 50%; /*fix for anchored animation*/ }
.ao-alignCenter {display:block; margin-left:auto; margin-right:auto;}
.ao-inlineTable { display:block; }
.ao-leftFullWidth { padding-right:100%; }
.ao-rightFullWidth { padding-left:100%; }
.anchorGroup { position:relative; };
div.ao-alignLeft:before, div.ao-leftFullWidth:before { content:''; float:left; display:none; }
div.ao-alignRight:before, div.ao-leftFullRight:before { content:''; float:right; display:none; }
.anchorGroup > .mso, .anchorGroup > .group { position:relative; }

ul.thumbs {
	padding:20px; white-space:nowrap; width:1px;
}

ul.thumbs li { /*Baker index thumbnails*/
    display:inline; list-style-type:none;
    margin:0 4px; outline:none;
    vertical-align:top;
}
ul.thumbs > li img { box-shadow:0 0 4px rgba(0, 0, 0, 0.5); }
.baker audio[controls] {min-width:400px;} /*fix progress bar*/

@media print { 
	#container,.page-scale-wrap{transform:none !important;}
	[class*=" btn-form-"]{display:none;}
}

:-ms-fullscreen .paper-horizontal { width:auto;height:auto;position:initial;margin:auto; }
:-moz-full-screen .paper-horizontal { width:auto;height:auto;position:initial;margin:auto; }
:-webkit-full-screen .paper-horizontal { width:auto;height:auto;position:initial;margin:auto; }

:-ms-fullscreen .paper-vertical .page { display:none; }
:-moz-full-screen .paper-vertical .page { display:none; }
:-webkit-full-screen .paper-vertical .page { display:none; }

:-ms-fullscreen .paper-horizontal .page { display:none; }
:-moz-full-screen .paper-horizontal .page { display:none; }
:-webkit-full-screen .paper-horizontal .page { display:none; }

:-ms-fullscreen .page.activePage { display:block; }
:-moz-full-screen .page.activePage { display:block; }
:-webkit-full-screen .page.activePage { display:block; }

:-webkit-full-screen .hideFS { display:none; }
:-ms-fullscreen .hideFS { display:none; }
:-moz-full-screen .hideFS { display:none; }

/*CSS Generated from InDesign Styles*/

body, #container-wrap { background:#ededed; }
:-webkit-full-screen { background:#ededed; }
#loadIndicator span:after { padding-left:1ex; content:'Loading content...'; }
#slider { width:1625px; height:875px; }
.page-scale-wrap { width:1625px; height:875px; background: #fff !important; }
.paper-vertical { width:1625px; height:100%; }
.paper-horizontal { width:19620px; height:887px; }
nav#page-nav { display:none; }
html.nav-transition nav#page-nav { opacity:.3; pointer-events:none; }
nav#page-nav > button {
	display:block; position:fixed;
	left:0;
	top:50%;
	width:68px;
	height:68px;
	margin:-34px 0 0 0; /*.5 height*/
	text-align:center;
	outline:0;
	background:url(../images/arrows-slider.svg) no-repeat;
	opacity:.7;
	will-change:transform;
}	
nav#page-nav > button:hover { opacity:1; }
nav#page-nav > #backBtn { left:0; }
html[data-dir="rtl"] nav#page-nav > #backBtn, html:not([data-dir="rtl"]) nav#page-nav > #nextBtn { 
	left:auto;
	right:0; 
	-ms-transform:scaleX(-1);
	-moz-transform:scaleX(-1);
	-webkit-transform:scaleX(-1);
	transform:scaleX(-1);
}
@media all and (max-height:875px) {
	.paper-horizontal { margin:0;top:0;}
	#container > .page { margin:0 auto;}/*pos top for webkit+baker*/
}
@media all and (max-width:1625px) {
	.paper-vertical .page, .paper-horizontal .page, .anythingSlider-in5 { padding:0;top:0;border:0; }
	.paper-vertical, .paper-horizontal { padding:0; }
}
#viewer-options-wrap {
	position:fixed;bottom:0;left:0;width:100%;padding:4px;
	font-family:'Gill Sans','Arial Narrow',Helvetica,Arial,sans-serif;
	transform:translateZ(0);
	transition:transform .5s;
}
#viewer-options-bar{
	position:absolute;bottom:0;left:0;width:100%;
	background-color:#000; color:#fff; fill:#fff;
	height:40px; display:table;
}
#viewer-options-bar.light { color:#000; fill:#000; }
#viewer-options-wrap.no-options #viewer-options-bar {display:none;}
#viewer-options-bar > span { display:table-cell; vertical-align:middle; cursor:default;}
#viewer-logo { display:inline-block; margin-right:14px;}
#viewer-logo img, #viewer-logo object {
	height:36px;
    vertical-align:middle;
    padding:2px 4px;
}
#viewer-title{ padding-left:6px; white-space:nowrap; }
#viewer-pagecount{ text-align:center; width:100%; }
#viewer-options { text-align:right; min-width:340px; }
.viewer-option-btn { cursor:pointer;display:inline-block;margin-right:24px;width:24px;padding:0;}
.viewer-option-btn > svg { position:relative; }
::-webkit-scrollbar #viewer-thumb-wrap {
    width:0px;  background:transparent;
}
#viewer-thumb-wrap {
    position:fixed; bottom:-50px; padding-bottom:46px;
    left:0; right:0; margin:auto;
    overflow-y:hidden; overflow-x:auto;
    background-color:rgba(255,255,255,.7);
    -webkit-overflow-scrolling:touch;
    scroll-behavior: smooth;
}
#viewer-options-thumb-images { margin-bottom:30px; padding-left:24px}
.viewer-page-thumb {
    display:table-cell;
    cursor:pointer;
    padding:24px 0;
	pointer-events:auto;
	-ms-transform:scale(.9); -webkit-transform:scale(.9); transform:scale(.9);
	-webkit-transition:transform .4s; transition:transform .4s;
}
.viewer-page-thumb:hover {
	-ms-transform:scale(1); -webkit-transform:scale(1); transform:scale(1);
}
.viewer-page-thumb > img { box-shadow:0 0 24px rgba(0,0,0,.5); }
.viewer-page-thumb.active > img { outline:2px solid #ff9900; }
.dragging, .dragging * { cursor:move !important; }
.dragging, .dragging * { cursor:move !important; }
#viewer_progress_bar {width:0;left:0;height:6px;position:fixed;bottom:40px;background:#ff9900;}
#viewer-options-wrap.no-options #viewer_progress_bar {bottom:0;}
@media all and (-ms-high-contrast:none), (-ms-high-contrast:active) {
     .viewer-option-btn{height:40px} /*IE10/11 fix*/
}
#viewer-options-bar #view-toggle {
    position:absolute;
    right:8px;
    bottom:120px;
    display:block;
    width:70px;
    height:50px;
    border-radius:12px 12px 0 0;
    cursor:pointer;
    display:none;
}
#viewer-options-bar #view-toggle svg {
    position:absolute;
    left:0;right:0;top:0;bottom:0;
    margin:auto;
}
html[data-dir="rtl"] #viewer-thumb-wrap { direction:rtl; }
@media all and (-ms-high-contrast:none), (-ms-high-contrast:active) {
     .viewer-option-btn{height:40px} /*IE10/11 fix*/
}

@media all and (max-width:780px) {
	#viewer-thumb-wrap { bottom:-20px; }
	#viewer-options-wrap.collapsed {
		-ms-transform:translateY(120px);
		-webkit-transform:translateY(120px) translateZ(0);
		transform:translateY(120px) translateZ(0);
		transition:transform .5s;
	}
	#viewer-options-wrap.collapsed #view-toggle svg {
		-ms-transform:rotate(180deg);
		-webkit-transform:rotate(180deg);
		transform:rotate(180deg);
	}
	#viewer-options-wrap.collapsed #viewer-options-thumb-images { display:none; }
	#viewer-options-bar #view-toggle { display:block; }
	#viewer_progress_bar {bottom:120px;}
	#viewer-options-bar{height:120px;display:block;}
	#viewer-pagecount{right:24px;width:auto;}
	#viewer-options-bar > span{display:inline-block;position:absolute;padding-top:14px}
	#viewer-options{display:table;table-layout:fixed;width:98%;bottom:12px;}
	.viewer-option-btn{display:table-cell;height:auto;min-width:44px;max-width:50px;padding:12px;width:2%;position:relative;}
	.viewer-option-btn > svg {max-width:50px;position:absolute;left:0;right:0;bottom:10px;margin:auto;}
	.viewer-option-btn:first-child{padding-left:6px;}
	.viewer-option-btn:last-child{padding-right:18px;}
	#viewer-options-thumb-images{margin-bottom:90px;}
	body:not(.responsive) .viewer-page-thumb > img {max-width:175px;}
}

#item546 {
	width:102px !important;
	height:100px !important;
}

#item544 {
	width:102px !important;
	height:100px !important;
	left:100px !important;
	top:102px !important;
}

#item645 {
	width:141px !important;
	height:39px !important;
	border-radius:11px;
	background:#ff2a54;
}

#item621 {
	width:141px !important;
	height:39px !important;
	border-radius:11px;
	background:#fff;
}

#item597 {
	width:141px !important;
	height:39px !important;
	border-radius:11px;
	background:#c8102d;
}

#item595 {
	width:141px !important;
	height:39px !important;
	top:52px !important;
}

#item669 {
	width:115px !important;
	height:31px !important;
}

@keyframes fade-in-670 {
	0% {opacity:0;}
	100% {opacity:0.69;}
}
@-webkit-keyframes fade-in-670 {
	0% {opacity:0;}
	100% {opacity:0.69;}
}
@keyframes fade-in-681 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-681 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-721 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-721 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item736 {
	width:43px !important;
	height:43px !important;
	left:1583px !important;
	top:834px !important;
}

#item770 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item781 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item815 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item845 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

#item868 {
	width:829px !important;
	height:875px !important;
	left:796px !important;
	text-shadow:7px 7px 5px rgba(0,0,0,0.3);
}

#item869 {
	width:530px !important;
	height:562px !important;
	left:898px !important;
	top:11px !important;
}

@keyframes fade-in-869 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-869 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item870 {
	width:512px !important;
	height:155px !important;
	left:84px !important;
	top:563px !important;
	background:#e5e5e5;
}

@keyframes fade-in-870 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-870 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-871 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-871 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item871 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-872 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-872 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-892 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-892 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-895 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-895 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item896 {
	width:530px !important;
	height:562px !important;
	left:829px !important;
}

#item897 {
	width:602px !important;
	height:303px !important;
	top:517px !important;
	background:#fff;
}

#item898 {
	width:512px !important;
	height:155px !important;
	left:84px !important;
	top:563px !important;
	background:#e5e5e5;
}

@keyframes fade-in-898 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-898 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-899 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-899 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item899 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-900 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-900 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-920 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-920 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-923 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-923 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item924 {
	width:530px !important;
	height:562px !important;
	left:829px !important;
}

#item925 {
	width:602px !important;
	height:303px !important;
	top:517px !important;
	background:#fff;
}

#item926 {
	width:512px !important;
	height:155px !important;
	left:84px !important;
	top:563px !important;
	background:#e5e5e5;
}

@keyframes fade-in-926 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-926 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-927 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-927 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item927 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

#item928 {
	width:530px !important;
	height:562px !important;
	left:898px !important;
	top:11px !important;
}

@keyframes fade-in-928 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-928 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-929 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-929 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-949 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-949 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-952 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-952 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item953 {
	width:530px !important;
	height:562px !important;
	left:829px !important;
}

#item954 {
	width:602px !important;
	height:303px !important;
	top:517px !important;
	background:#fff;
}

#item955 {
	width:511px !important;
	height:554px !important;
	left:905px !important;
	top:15px !important;
}

@keyframes fade-in-955 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-955 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-978 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-978 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1035 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1035 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1051 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item1062 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item1096 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

@keyframes fade-in-1105 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1105 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes spring-left-1124 {
	0% {transform:translate(0px,0px);}
	100% {transform:translate(85px,-172px);}
}
@-webkit-keyframes spring-left-1124 {
	0% {-webkit-transform:translate(0px,0px);}
	100% {-webkit-transform:translate(85px,-172px);}
}
@keyframes spring-left-1125 {
	0% {transform:translate(0px,0px);}
	100% {transform:translate(-153px,49px);}
}
@-webkit-keyframes spring-left-1125 {
	0% {-webkit-transform:translate(0px,0px);}
	100% {-webkit-transform:translate(-153px,49px);}
}
@keyframes fade-in-1139 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1139 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1150 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

@keyframes fade-in-1219 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1219 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1241 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1241 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1263 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1263 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes twirl-1276 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-1276 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes twirl-1277 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-1277 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
#item1277 {
	transform-origin:0% 100%;
	-webkit-transform-origin:0% 100%;
}

@keyframes appear-1298 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-1298 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@keyframes fade-out-1301 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1301 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1302 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1302 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1303 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1303 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes twirl-1304 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-1304 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes appear-1326 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-1326 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@keyframes twirl-1329 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-1329 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes fade-out-1332 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1332 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1333 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1333 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1334 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1334 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1335 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1335 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1336 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1336 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-1337 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1337 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes appear-1357 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-1357 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
#item1410 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item1418 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item1429 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

@keyframes fade-out-1497 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-1497 {
	0% {opacity:1;}
	100% {opacity:0;}
}
#item1530 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

#item1553 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-1553 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1553 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1573 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1573 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-1576 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-1576 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item1576 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-1577 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1577 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1578 {
	width:543px !important;
	height:263px !important;
	left:80px !important;
	top:558px !important;
	background:#fff;
}

@keyframes fade-in-1578 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1578 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1579 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-1579 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1579 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1599 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1599 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-1602 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-1602 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item1602 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-1603 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1603 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1604 {
	width:543px !important;
	height:263px !important;
	left:80px !important;
	top:558px !important;
	background:#fff;
}

@keyframes fade-in-1604 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1604 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1605 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-1605 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1605 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1625 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1625 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-1628 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-1628 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item1628 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-1629 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1629 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1630 {
	width:590px !important;
	height:263px !important;
	left:80px !important;
	top:558px !important;
	background:#fff;
}

@keyframes fade-in-1630 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1630 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1631 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-1631 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1631 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1651 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1651 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-1654 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-1654 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item1654 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-1655 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1655 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1656 {
	width:590px !important;
	height:263px !important;
	left:80px !important;
	top:558px !important;
	background:#fff;
}

@keyframes fade-in-1656 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1656 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1657 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1657 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1729 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item1740 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item1751 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

@keyframes fade-in-1823 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1823 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1848 {
	width:43px !important;
	height:43px !important;
	left:1583px !important;
	top:834px !important;
}

@keyframes fade-in-1891 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1891 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1913 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1913 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1935 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1935 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1957 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1957 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-1979 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-1979 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item1993 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item2004 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item2015 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item2092 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-2092 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2092 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-2093 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-2093 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item2093 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-2094 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2094 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2114 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2114 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2117 {
	width:769px !important;
	height:350px !important;
	left:8px !important;
	top:512px !important;
	background:#fff;
}

@keyframes fade-in-2117 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2117 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2118 {
	width:512px !important;
	height:81px !important;
	left:84px !important;
	top:637px !important;
	background:#e5e5e5;
}

@keyframes fade-in-2118 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2118 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-2119 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-2119 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item2119 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-2139 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2139 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2142 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2142 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2143 {
	width:769px !important;
	height:350px !important;
	left:8px !important;
	top:512px !important;
	background:#fff;
}

@keyframes fade-in-2143 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2143 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2144 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-2144 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2144 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-2145 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-2145 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item2145 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-2146 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2146 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2166 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2166 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2169 {
	width:769px !important;
	height:350px !important;
	left:8px !important;
	top:512px !important;
	background:#fff;
}

@keyframes fade-in-2169 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2169 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2170 {
	width:512px !important;
	height:153px !important;
	left:84px !important;
	top:565px !important;
	background:#e5e5e5;
}

@keyframes fade-in-2170 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2170 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes grow-2171 {
	0% {transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
@-webkit-keyframes grow-2171 {
	0% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(0);}
	100% {-webkit-transform:translate(0px,0px) scaleX(1) scaleY(1);}
}
#item2171 {
	transform-origin:50% 100%;
	-webkit-transform-origin:50% 100%;
}

@keyframes fade-in-2172 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2172 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2192 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2192 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2195 {
	width:769px !important;
	height:350px !important;
	left:8px !important;
	top:512px !important;
	background:#fff;
}

@keyframes fade-in-2195 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2195 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2196 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2196 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2346 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2346 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2046 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

#item2278 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item2289 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item2323 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

@keyframes fade-in-2392 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2392 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item2406 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

@keyframes fade-in-2475 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2475 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2497 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2497 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-2519 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2519 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes twirl-2530 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-2530 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes twirl-2535 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-2535 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes twirl-2536 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-2536 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
#item2536 {
	transform-origin:0% 100%;
	-webkit-transform-origin:0% 100%;
}

@keyframes appear-2557 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-2557 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@keyframes fade-out-2560 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2560 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2561 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2561 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2562 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2562 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes twirl-2563 {
	0% {transform:translate(0px,0px) rotate(0deg);}
	100% {transform:translate(0px,0px) rotate(360deg);}
}
@-webkit-keyframes twirl-2563 {
	0% {-webkit-transform:translate(0px,0px) rotate(0deg);}
	100% {-webkit-transform:translate(0px,0px) rotate(360deg);}
}
@keyframes appear-2585 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-2585 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@keyframes fade-out-2588 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2588 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2589 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2589 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2590 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2590 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2591 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2591 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2592 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2592 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes fade-out-2593 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2593 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@keyframes appear-2615 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-2615 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:1;}
	100% {opacity:1;}
}
#item2648 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item2656 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item2667 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

@keyframes appear-2685 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:0.73;}
	100% {opacity:0.73;}
}
@-webkit-keyframes appear-2685 {
	0% {opacity:0;}
	57% {opacity:0;}
	64% {opacity:0.73;}
	100% {opacity:0.73;}
}
#item2724 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

@keyframes fade-out-2792 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-2792 {
	0% {opacity:1;}
	100% {opacity:0;}
}
#item2802 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item2813 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item2824 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item2848 {
	width:43px !important;
	height:43px !important;
	left:1583px !important;
	top:834px !important;
}

#item2863 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:220px !important;
	border:solid 2px #b2b2b2;
}

#item2864 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:284px !important;
	border:solid 2px #b2b2b2;
}

#item2865 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:348px !important;
	border:solid 2px #b2b2b2;
}

#item2866 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:414px !important;
	border:solid 2px #b2b2b2;
}

#item2867 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:506px !important;
	border:solid 2px #b2b2b2;
}

#item2868 {
	width:28px !important;
	height:28px !important;
	left:761px !important;
	top:572px !important;
	border:solid 2px #b2b2b2;
}

@keyframes fade-in-2869 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-2869 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes appear-3072 {
	0% {opacity:0;}
	83% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes appear-3072 {
	0% {opacity:0;}
	83% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3094 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3094 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3116 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3116 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3138 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3138 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3160 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3160 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3182 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3182 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3185 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3185 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3505 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3505 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-3825 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-3825 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-4145 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-4145 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-4465 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-4465 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@keyframes fade-in-4785 {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fade-in-4785 {
	0% {opacity:0;}
	100% {opacity:1;}
}
#item4798 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item4809 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item4843 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

@keyframes fade-out-4883 {
	0% {opacity:1;}
	100% {opacity:0;}
}
@-webkit-keyframes fade-out-4883 {
	0% {opacity:1;}
	100% {opacity:0;}
}
#item4897 {
	width:43px !important;
	height:43px !important;
	left:1582px !important;
	top:832px !important;
}

#item4957 {
	width:17px !important;
	height:17px !important;
	left:1521px !important;
	top:844px !important;
}

#item4968 {
	width:17px !important;
	height:17px !important;
	left:1544px !important;
	top:844px !important;
}

#item5002 {
	width:17px !important;
	height:17px !important;
	left:1566px !important;
	top:843px !important;
}

#item5054 {
	width:39px !important;
	height:39px !important;
}

#item5052 {
	width:39px !important;
	height:39px !important;
	left:100px !important;
	top:544px !important;
}

#item5058 {
	width:43px !important;
	height:42px !important;
}

#item5056 {
	width:43px !important;
	height:42px !important;
	left:153px !important;
	top:543px !important;
}

#item5062 {
	width:43px !important;
	height:43px !important;
}

#item5060 {
	width:43px !important;
	height:43px !important;
	left:206px !important;
	top:542px !important;
}

#item5066 {
	width:43px !important;
	height:45px !important;
}

#item5064 {
	width:43px !important;
	height:45px !important;
	left:260px !important;
	top:541px !important;
}

#item5070 {
	width:45px !important;
	height:43px !important;
}

#item5068 {
	width:45px !important;
	height:43px !important;
	left:313px !important;
	top:542px !important;
}

#item5096 {
	width:102px !important;
	height:100px !important;
}

#item5094 {
	width:102px !important;
	height:100px !important;
	left:97px !important;
	top:101px !important;
}

#item5712 {
	width:238px !important;
	height:32px !important;
	left:83px !important;
	top:597px !important;
}

#item5730 {
	width:163px !important;
	height:30px !important;
	left:340px !important;
	top:597px !important;
}

#item5766 {
	width:1px !important;
	height:9px !important;
	left:6px !important;
	top:43px !important;
	background:#fff;
}
#in5footer,  #prefooter {display:none;}
