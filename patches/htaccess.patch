--- config/.htaccess	2025-02-27 17:40:49.253564237 +0000
+++ config/.htaccess	2025-04-14 18:46:24.969713780 +0000
@@ -2,8 +2,9 @@
 # Apache/PHP/Drupal settings:
 #
 
+
 # Protect files and directories from prying eyes.
-<FilesMatch "\.(engine|inc|install|make|module|profile|po|sh|.*sql|theme|twig|tpl(\.php)?|xtmpl|yml)(~|\.sw[op]|\.bak|\.orig|\.save)?$|^(\.(?!well-known).*|Entries.*|Repository|Root|Tag|Template|composer\.(json|lock)|web\.config|yarn\.lock|package\.json)$|^#.*#$|\.php(~|\.sw[op]|\.bak|\.orig|\.save)$">
+<FilesMatch "\.(engine|inc|install|make|module|profile|po|sh|.*sql|theme|twig|tpl(\.php)?|xtmpl|yml)(~|\.sw[op]|\.bak|\.orig|\.save)?$|^(\.(?!well-known).*|Entries.*|Repository|Root|Tag|Template|composer\.(json|lock)|web\.config|yarn\.lock|package\.json)$|^#.*#$|\.php(~|\.sw[op]|\.bak|\.orig|\.save)|\.md|\.txt$">
   <IfModule mod_authz_core.c>
     Require all denied
   </IfModule>
@@ -12,6 +13,13 @@
   </IfModule>
 </FilesMatch>
 
+# HELPDESK-7344 - Reduce PDF caching down to 10 minutes
+<FilesMatch "\.pdf$">
+  <IfModule mod_headers.c>
+    Header set Cache-Control "max-age=600, must-revalidate"
+  </IfModule>
+</FilesMatch>
+
 # Don't show directory listings for URLs which map to a directory.
 Options -Indexes
 
@@ -60,12 +68,74 @@
 <IfModule mod_rewrite.c>
   RewriteEngine on
 
-  # Set "protossl" to "s" if we were accessed via https://.  This is used later
+  # Redirecting http://www.domain.com and https://www.domain.com
+  # to https://domain.com
+  RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
+  RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [L,R=301]
+  # Redirecting http://domain.com to https://domain.com
+  RewriteCond %{HTTPS} off
+  # ignore .versantus.co.uk domains, as this would indicate it's a test system and we don't
+  # want to redirect
+  RewriteCond %{HTTP_HOST} !\.versantus\.co\.uk$ [NC]
+  RewriteCond %{HTTP:X-Forwarded-Proto} !https
+  RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
+
+ # Campaign redirects
+  RewriteRule ^uk/en/campaign/download/IFRS16-guide$ /campaigns/IFRS16-guide [L]
+  RewriteRule ^uk/en/campaign/download/retail-guide$ /campaigns/retail-guide [L]
+  RewriteRule ^uk/en/campaign/download/transport-and-logistics-guide$ /campaigns/transport-and-logistics-guide [L]
+  RewriteRule ^uk/en/campaign/download/iTrent-guide$ /campaigns/itrent-guide [L]
+  RewriteRule ^uk/en/knowledge-hub/hr/itrent-reinvigoration-guide-download$ /campaigns/itrent-reinvigoration-guide [L]
+  RewriteRule ^uk/en/campaign/download/irish-guide$ /campaigns/irish-guide [L]
+  RewriteRule ^uk/en/campaign/download/Managed-Data-Service-Brochure$ /campaigns/managed-data-service-brochure [L]
+  RewriteRule ^uk/en/campaign/download/finance-report$ /campaigns/finance-report [L]
+  RewriteRule ^uk/en/knowledge-hub/data-analytics/data-and-analytics-consultancy-guide-download$ /campaigns/data-and-analytics-consultancy-guide [L]
+  RewriteRule ^uk/en/campaign/download/healthy-recruitment-guide$ /campaigns/healthy-recruitment-guide [L]
+  RewriteRule ^uk/en/knowledge-hub/hr/healthy-recruitment-guide-download$ /campaigns/healthy-recruitment-guide [L]
+  RewriteRule ^uk/en/campaign/download/employee-experience-report$ /campaigns/employee-experience-report [L]
+  RewriteRule ^campaign/download/payroll-challenges-guide$ /campaigns/payroll-challenges-guide [L]
+  RewriteRule ^campaign/download/payroll-research-report$ /campaigns/payroll-research-report [L]
+
+  #Miscellaneous redirects
+  RewriteRule ^peoplefirst$ https://mhrglobal.com/uk/en/peoplefirst [R=301,L,QSA]
+  RewriteRule ^uk/en/demo$ https://mhrglobal.com/uk/en/request-demo [R=302,L,QSA]
+  RewriteRule ^explore/mhr$ https://mhrglobal.com/ie/en [R=302,L,QSA]
+  RewriteRule ^downloads/hr-chatbot-digital-brochure$ https://mhrglobal.com/ie/en/downloads/hr-chatbot-brochure [R=302,L,QSA]
+  RewriteRule ^uk/en/campaign/download/data-and-analytics-consultancy-guide/$ https://mhrglobal.com/uk/en/knowledge-hub/data-analytics/data-and-analytics-consultancy-guide-download [R=302,L,QSA]
+  RewriteRule ^uk/en/campaign/download/iTrent-Reinvigoration-Guide/$ https://mhrglobal.com/uk/en/knowledge-hub/hr/itrent-reinvigoration-guide-download [R=302,L,QSA]
+
+  #US site relaunch redirects
+  RewriteRule ^us/en/knowledge-hub/power-check-ins-7-proven-strategies$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/about-us/sustainability/generations$ https://mhrglobal.com/us/en/about-us/sustainability [R=301,L,QSA]
+  RewriteRule ^us/en/industry/manufacturing$ https://mhrglobal.com/us/en [R=301,L,QSA]
+  RewriteRule ^us/en/knowledge-hub/mhr-news/looking-ahead-podcast$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/industry/healthcare$ https://mhrglobal.com/us/en [R=301,L,QSA]
+  RewriteRule ^us/en/campaign/download/itrent-brochure-thank-you$ https://mhrglobal.com/us/en [R=301,L,QSA]
+  RewriteRule ^us/en/knowledge-hub/hr/business-resilience-eguide-download$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/knowledge-hub/hr/hr-managers-checklists-download$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/knowledge-hub/payroll/clocking-guide-download$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/about-us/reliability$ https://mhrglobal.com/us/en/about-us/sustainability [R=301,L,QSA]
+  RewriteRule ^us/en/products/assist$ https://mhrglobal.com/us/en/support [R=301,L,QSA]
+  RewriteRule ^us/en/download/business-resilience-eguide-thank-you$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/careers/beyond-the-tie/laura-lindsey$ https://mhrglobal.com/us/en/about-us [R=301,L,QSA]
+  RewriteRule ^us/en/careers/beyond-the-tie/stacey-hirons$ https://mhrglobal.com/us/en/about-us [R=301,L,QSA]
+  RewriteRule ^us/en/explore/workforce-resilience$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/always-on/download/finance-checklists-thank-you$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/careers/beyond-the-tie/simon-davies$ https://mhrglobal.com/us/en/about-us [R=301,L,QSA]
+  RewriteRule ^us/en/careers/beyond-the-tie/lynn-holdsworth$ https://mhrglobal.com/us/en/about-us [R=301,L,QSA]
+  RewriteRule ^us/en/1046-customer-showcase-thank-you$ https://mhrglobal.com/us/en [R=301,L,QSA]
+  RewriteRule ^us/en/careers/beyond-the-tie/joanna-coleby$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/public-sector-support$ https://mhrglobal.com/us/en/support [R=301,L,QSA]
+  RewriteRule ^us/en/contact-us/assist$ https://mhrglobal.com/us/en/contact-us [R=301,L,QSA]
+  RewriteRule ^us/en/software-integrations-services-education-thank-you$ https://mhrglobal.com/us/en [R=301,L,QSA]
+  RewriteRule ^us/en/campaign/download/payroll-research-report-thank-you$ https://mhrglobal.com/us/en/knowledge-hub [R=301,L,QSA]
+  RewriteRule ^us/en/learning$ https://mhrglobal.com/us/en/learning-management-system [R=301,L,QSA]
+
   # if you enable "www." stripping or enforcement, in order to ensure that
   # you don't bounce between http and https.
-  RewriteRule ^ - [E=protossl]
-  RewriteCond %{HTTPS} on
-  RewriteRule ^ - [E=protossl:s]
+  # RewriteRule ^ - [E=protossl]
+  # RewriteCond %{HTTPS} on
+  # RewriteRule ^ - [E=protossl:s]
 
   # Make sure Authorization HTTP header is available to PHP
   # even when running as CGI or FastCGI.
