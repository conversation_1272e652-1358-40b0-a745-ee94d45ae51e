#!/bin/bash

echo "Updating domain settings"

DRUSH="/usr/bin/drush --root=/home/<USER>/project_git/drupal/web -y "

${DRUSH} cset domain.record.default hostname mhr.staging.versantus.co.uk
${DRUSH} cset domain.record.ie hostname mhr.staging.versantus.co.uk
${DRUSH} cset domain.record.uk hostname mhr.staging.versantus.co.uk
${DRUSH} cset domain.record.us hostname mhr.staging.versantus.co.uk
${DRUSH} cr

echo "Done";

echo "Setting up reroute email"
${DRUSH} en reroute_email
${DRUSH} cset reroute_email.settings address "<EMAIL>"
echo "Done";

echo "Setting up stage file proxy"
${DRUSH} en stage_file_proxy
${DRUSH} cset stage_file_proxy.settings origin "https://www.mhrglobal.com"
echo "Done";

