# VWO Integration Documentation

## Tickets:
- https://versantus.atlassian.net/browse/HELPDESK-3673 - Initial install and config

## What is it?
VWO (Visual Website Optimizer) testing is a platform that helps businesses improve their websites. It enables them to run experiments and test different variations of web elements to enhance user experiences and increase conversions. With VWO, businesses can make data-driven decisions to optimize their websites and achieve their goals.

## How do we integrate it?
To integrate VWO into our project, we will use the VWO Drupal module. This module provides a seamless and future-proof way to integrate VWO with Drupal websites.

- VWO Drupal instructions: [VWO Drupal Instructions](https://help.vwo.com/hc/en-us/articles/************)
- Drupal module: [VWO Drupal Module](https://www.drupal.org/project/vwo)

## General Settings
The VWO settings page can be accessed at the following URL:

- [VWO General Settings](https://mhrglobal.com/uk/en/admin/config/system/vwo)

I have already configured it to use the account ID provided.
ID is: 713459

By default, the settings should work fine, and you may not need to change anything here. However, if any issues arise, we might need to make adjustments in this section.

## Visibility Settings
The visibility settings allow you to control where the VWO tests can run based, limiting it to specific content types, user roles, and page paths. You can configure these settings at the following URL:

- [VWO Visibility Settings](https://mhrglobal.com/uk/en/admin/config/system/vwo/visibility)

## Extract Account ID Tab
The "Extract Account ID" tab automates the task of finding the account ID from the provided smart code. I have manually located the account ID, we don't need to use this feature. In case the account is not communicating with Drupal, you can paste the code in this tab after removing the ID from the General settings tab. But I believe we won't need to use it.

- [VWO Extract Account ID](https://mhrglobal.com/uk/en/admin/config/system/vwo/vwoid)

## Permissions
Currently, only users with the "Client Administrator" role have permission to adjust the VWO settings.

## Environment-Based Testing (Configuring for Staging)
To control the testing environment, all configuration is done within the VWO account. Although I'm not sure since I don't have access to the account, I assume we need to provide the URL/domain for the testing environment. In this case, you will likely need to pass the basic auth credentials along with the URL, as shown in the example below:

- "https://mhr:<EMAIL>"

## Further Docs
For more information on environment-specific reporting in VWO FullStack, refer to [VWO's documentation](https://help.vwo.com/hc/en-us/articles/************-Environment-specific-Reporting-in-VWO-FullStack).
